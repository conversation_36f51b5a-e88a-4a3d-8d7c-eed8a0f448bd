"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ProductCarousel.tsx":
/*!********************************************!*\
  !*** ./src/components/ProductCarousel.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductCarousel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _AuthModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthModal */ \"(app-pages-browser)/./src/components/AuthModal.tsx\");\n/* harmony import */ var _CartModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CartModal */ \"(app-pages-browser)/./src/components/CartModal.tsx\");\n/* harmony import */ var _barrel_optimize_names_ShoppingCart_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ShoppingCart,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ShoppingCart_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ShoppingCart,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ProductCarousel(param) {\n    let { products, onProductSelect, onShopAccess, mode } = param;\n    _s();\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAutoPlaying, setIsAutoPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mode === 'catalog');\n    const filteredProducts = products;\n    // Stati per autenticazione e carrello\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCartModal, setShowCartModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [authMode, setAuthMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('login');\n    const [pendingAction, setPendingAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pendingProduct, setPendingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Hook per autenticazione e carrello\n    const { isAuthenticated, addToCart } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    // Funzioni per gestire carrello e acquisto\n    const handleAddToCart = (product)=>{\n        if (!isAuthenticated) {\n            setPendingAction('cart');\n            setPendingProduct(product);\n            setAuthMode('login');\n            setShowAuthModal(true);\n        } else {\n            addToCart({\n                id: product.id || product._id,\n                name: product.name,\n                price: product.price,\n                image_url: product.image_url\n            });\n            // Mostra feedback visivo\n            console.log('Prodotto aggiunto al carrello:', product.name);\n        }\n    };\n    const handleBuyNow = async (product)=>{\n        if (!isAuthenticated) {\n            setPendingAction('buy');\n            setPendingProduct(product);\n            setAuthMode('login');\n            setShowAuthModal(true);\n        } else {\n            // Procedi direttamente al checkout\n            await handleDirectCheckout(product);\n        }\n    };\n    const handleDirectCheckout = async (product)=>{\n        try {\n            const response = await fetch('/api/checkout', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    items: [\n                        {\n                            id: product.id || product._id,\n                            name: product.name,\n                            price: product.price,\n                            quantity: 1,\n                            image_url: product.image_url\n                        }\n                    ],\n                    totalAmount: product.price\n                })\n            });\n            const { url } = await response.json();\n            if (url) {\n                window.location.href = url;\n            }\n        } catch (error) {\n            console.error('Errore durante il checkout diretto:', error);\n        }\n    };\n    const handleAuthSuccess = async (userData)=>{\n        setShowAuthModal(false);\n        if (pendingAction && pendingProduct) {\n            if (pendingAction === 'cart') {\n                addToCart({\n                    id: pendingProduct.id || pendingProduct._id,\n                    name: pendingProduct.name,\n                    price: pendingProduct.price,\n                    image_url: pendingProduct.image_url\n                });\n            } else if (pendingAction === 'buy') {\n                await handleDirectCheckout(pendingProduct);\n            }\n        }\n        // Reset pending actions\n        setPendingAction(null);\n        setPendingProduct(null);\n    };\n    // Auto-play carousel\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductCarousel.useEffect\": ()=>{\n            if (!isAutoPlaying || filteredProducts.length <= 1) return;\n            const interval = setInterval({\n                \"ProductCarousel.useEffect.interval\": ()=>{\n                    setCurrentIndex({\n                        \"ProductCarousel.useEffect.interval\": (prev)=>(prev + 1) % filteredProducts.length\n                    }[\"ProductCarousel.useEffect.interval\"]);\n                }\n            }[\"ProductCarousel.useEffect.interval\"], 4000);\n            return ({\n                \"ProductCarousel.useEffect\": ()=>clearInterval(interval)\n            })[\"ProductCarousel.useEffect\"];\n        }\n    }[\"ProductCarousel.useEffect\"], [\n        isAutoPlaying,\n        filteredProducts.length\n    ]);\n    const getProductIcon = (identifier)=>{\n        // Handle both slug and id, with safety check\n        if (!identifier) return '💎';\n        const id = identifier.toLowerCase();\n        if (id.includes('telegram') || id.includes('bot')) return '🤖';\n        if (id.includes('portfolio') || id.includes('website')) return '🌐';\n        if (id.includes('notion') || id.includes('template')) return '⚡';\n        if (id.includes('ai') || id.includes('content')) return '🧠';\n        if (id.includes('ecommerce') || id.includes('store')) return '🛒';\n        if (id.includes('social') || id.includes('media')) return '📱';\n        if (id.includes('analytics') || id.includes('dashboard')) return '📊';\n        if (id.includes('seo') || id.includes('optimizer')) return '🔍';\n        if (id.includes('image') || id.includes('generator')) return '🎨';\n        if (id.includes('suite') || id.includes('complete')) return '🚀';\n        return '💎';\n    };\n    const nextProduct = ()=>{\n        setCurrentIndex((prev)=>(prev + 1) % filteredProducts.length);\n    };\n    const prevProduct = ()=>{\n        setCurrentIndex((prev)=>(prev - 1 + filteredProducts.length) % filteredProducts.length);\n    };\n    if (products.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4 opacity-50\",\n                        children: \"\\uD83D\\uDECD️\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Nessun prodotto disponibile\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n            lineNumber: 161,\n            columnNumber: 7\n        }, this);\n    }\n    if (mode === 'single' && products.length === 1) {\n        const product = products[0];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                scale: 0.9\n            },\n            animate: {\n                opacity: 1,\n                scale: 1\n            },\n            className: \"h-full flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-2\",\n                            children: \"Prodotto Suggerito\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-1 bg-aurora rounded-full\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    whileHover: {\n                        scale: 1.02\n                    },\n                    onClick: ()=>onProductSelect(product),\n                    className: \"card-aurora cursor-pointer flex-1 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full h-64 bg-gradient-to-br from-purple-600/20 to-blue-600/20 rounded-xl mb-4 flex items-center justify-center overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                whileHover: {\n                                    scale: 1.1\n                                },\n                                className: \"text-8xl opacity-70\",\n                                children: getProductIcon(product.id || product.slug)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-white mb-2\",\n                                    children: [\n                                        product.name,\n                                        \" \\uD83D\\uDD25 MODIFICATO\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                    whileHover: {\n                                        scale: 1.02,\n                                        x: 2\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    onClick: ()=>{\n                                        console.log('🔍 Dettagli clicked for:', product.name);\n                                        onProductSelect === null || onProductSelect === void 0 ? void 0 : onProductSelect(product);\n                                    },\n                                    className: \"inline-flex items-center space-x-1 text-xs text-white/70 hover:text-white transition-colors mb-3 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"border-b border-dotted border-white/30 group-hover:border-white/60 transition-colors\",\n                                            children: \"Visualizza dettagli\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3 h-3 transform group-hover:translate-x-0.5 transition-transform\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 5l7 7-7 7\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-sm leading-relaxed line-clamp-2 mb-4\",\n                                    children: product.description\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: [\n                                            \"€\",\n                                            product.price\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                handleAddToCart(product);\n                                            },\n                                            className: \"flex-1 bg-white/10 hover:bg-white/20 text-white py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-1 border border-white/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ShoppingCart_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Carrello\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                handleBuyNow(product);\n                                            },\n                                            className: \"flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ShoppingCart_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Acquista\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2 mt-4\",\n                            children: product.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 bg-purple-600/20 text-purple-300 text-xs rounded-full border border-purple-500/30\",\n                                    children: tag\n                                }, tag, false, {\n                                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n            lineNumber: 173,\n            columnNumber: 7\n        }, this);\n    }\n    // Debug\n    console.log('ProductCarousel - Products:', products.length, 'Filtered:', filteredProducts.length);\n    console.log('🎨 ProductCarousel rendering with modifications - no price, with details button');\n    // Alert visibile per debug\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ProductCarousel.useEffect\": ()=>{\n            console.log('🚀 COMPONENTE MODIFICATO - VERSIONE AGGIORNATA CARICATA!');\n        }\n    }[\"ProductCarousel.useEffect\"], []);\n    // Forza currentIndex a 0 per prodotto singolo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductCarousel.useEffect\": ()=>{\n            if (filteredProducts.length === 1) {\n                setCurrentIndex(0);\n                console.log('Single product detected, setting currentIndex to 0');\n            }\n        }\n    }[\"ProductCarousel.useEffect\"], [\n        filteredProducts.length\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative perspective-1000 h-64\",\n                children: [\n                    filteredProducts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-white/60\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-2\",\n                                    children: \"\\uD83C\\uDF1F\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    children: \"Caricamento prodotti...\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full overflow-visible\",\n                        onMouseEnter: ()=>setIsAutoPlaying(false),\n                        onMouseLeave: ()=>setIsAutoPlaying(mode === 'catalog'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-full h-full\",\n                                    children: filteredProducts.map((product, index)=>{\n                                        const offset = index - currentIndex;\n                                        const absOffset = Math.abs(offset);\n                                        // Logica speciale per prodotto singolo\n                                        const isSingleProduct = filteredProducts.length === 1;\n                                        const isCenter = offset === 0 || isSingleProduct;\n                                        // Calcolo posizione e scala per effetto 3D\n                                        const scale = isCenter ? 1.2 : Math.max(0.6, 1 - absOffset * 0.2);\n                                        const opacity = isCenter ? 1 : Math.max(0.3, 1 - absOffset * 0.3);\n                                        const rotateY = isSingleProduct ? 0 : offset * 25; // No rotazione per prodotto singolo\n                                        const translateX = isSingleProduct ? 0 : offset * 180; // Centrato per prodotto singolo\n                                        const translateZ = isCenter ? 0 : -100 * absOffset; // Profondità\n                                        console.log(\"Product \".concat(index, \": offset=\").concat(offset, \", isCenter=\").concat(isCenter, \", isSingle=\").concat(isSingleProduct));\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            animate: {\n                                                scale,\n                                                opacity,\n                                                rotateY,\n                                                x: translateX,\n                                                z: translateZ\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                ease: [\n                                                    0.25,\n                                                    0.46,\n                                                    0.45,\n                                                    0.94\n                                                ] // Apple-like easing\n                                            },\n                                            className: \"absolute w-40 h-48 cursor-pointer\",\n                                            style: {\n                                                transformStyle: 'preserve-3d',\n                                                filter: isCenter ? 'brightness(1.1) drop-shadow(0 20px 40px rgba(142, 45, 226, 0.3))' : 'brightness(0.8)',\n                                                left: '50%',\n                                                top: '50%',\n                                                marginLeft: '-80px',\n                                                marginTop: '-96px'\n                                            },\n                                            onClick: ()=>{\n                                                if (isCenter) {\n                                                    onProductSelect(product);\n                                                } else {\n                                                    setCurrentIndex(index);\n                                                }\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card-aurora h-full flex flex-col consciousness-awakening\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative w-full h-28 rounded-md mb-2 flex items-center justify-center overflow-hidden\",\n                                                        style: {\n                                                            background: \"linear-gradient(135deg,\\n                            rgba(142, 45, 226, 0.2),\\n                            rgba(0, 216, 182, 0.2)\\n                          )\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                                whileHover: isCenter ? {\n                                                                    scale: 1.1,\n                                                                    rotate: 5\n                                                                } : {},\n                                                                transition: {\n                                                                    duration: 0.3\n                                                                },\n                                                                className: \"text-3xl opacity-80\",\n                                                                children: getProductIcon(product.id || product.slug)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 neural-wave opacity-10 rounded-xl\",\n                                                                style: {\n                                                                    background: \"linear-gradient(45deg,\\n                              rgba(142, 45, 226, 0.1),\\n                              rgba(0, 216, 182, 0.1)\\n                            )\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 text-center px-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-sm font-semibold mb-2 line-clamp-2\",\n                                                                style: {\n                                                                    color: 'var(--aurora-champagne)'\n                                                                },\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                                whileHover: {\n                                                                    scale: 1.02,\n                                                                    y: -1\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.98\n                                                                },\n                                                                onClick: ()=>{\n                                                                    console.log('🔍 Dettagli clicked for:', product.name);\n                                                                    onProductSelect === null || onProductSelect === void 0 ? void 0 : onProductSelect(product);\n                                                                },\n                                                                className: \"inline-flex items-center space-x-1 text-xs text-white/60 hover:text-white/90 transition-all duration-200 group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"border-b border-dotted border-white/20 group-hover:border-white/50 transition-colors\",\n                                                                        children: \"Dettagli\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-3 h-3 transform group-hover:translate-x-0.5 transition-transform duration-200\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M9 5l7 7-7 7\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                                            lineNumber: 424,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, product.id || product._id || product.slug, false, {\n                                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this),\n                            filteredProducts.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.1,\n                                            x: -8\n                                        },\n                                        whileTap: {\n                                            scale: 0.9\n                                        },\n                                        onClick: prevProduct,\n                                        className: \"absolute left-2 top-1/2 transform -translate-y-1/2 w-8 h-8 glass border border-white/20 rounded-full flex items-center justify-center text-white transition-all z-20 shadow-lg\",\n                                        style: {\n                                            background: 'linear-gradient(135deg, var(--aurora-purple), var(--aurora-teal))',\n                                            color: 'var(--aurora-champagne)'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                        whileHover: {\n                                            scale: 1.1,\n                                            x: 8\n                                        },\n                                        whileTap: {\n                                            scale: 0.9\n                                        },\n                                        onClick: nextProduct,\n                                        className: \"absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 glass border border-white/20 rounded-full flex items-center justify-center text-white transition-all z-20 shadow-lg\",\n                                        style: {\n                                            background: 'linear-gradient(135deg, var(--aurora-purple), var(--aurora-teal))',\n                                            color: 'var(--aurora-champagne)'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 5l7 7-7 7\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this),\n                    filteredProducts.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mt-4 space-x-2\",\n                        children: filteredProducts.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                whileHover: {\n                                    scale: 1.2\n                                },\n                                whileTap: {\n                                    scale: 0.8\n                                },\n                                onClick: ()=>setCurrentIndex(index),\n                                className: \"w-2 h-2 rounded-full transition-all duration-300\",\n                                style: {\n                                    backgroundColor: index === currentIndex ? 'var(--aurora-teal)' : 'var(--aurora-charcoal)',\n                                    boxShadow: index === currentIndex ? '0 0 10px rgba(0, 216, 182, 0.5)' : 'none'\n                                }\n                            }, index, false, {\n                                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 11\n                    }, this),\n                    filteredProducts.length === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 glass border border-white/20 rounded-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white/70 text-sm\",\n                                children: \"Prodotto unico per questa categoria\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                            whileHover: {\n                                scale: 1.05,\n                                boxShadow: '0 15px 35px rgba(0, 0, 0, 0.8), 0 0 30px rgba(212, 175, 55, 0.3)'\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            onClick: onShopAccess,\n                            className: \"group relative px-5 py-2 rounded-lg font-bold text-xs text-white overflow-hidden transition-all duration-500 border border-white/10\",\n                            style: {\n                                background: \"\\n                linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(20, 20, 20, 0.95) 50%, rgba(0, 0, 0, 0.9) 100%),\\n                radial-gradient(circle at 50% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 70%)\\n              \",\n                                backdropFilter: 'blur(20px)',\n                                boxShadow: \"\\n                0 8px 32px rgba(0, 0, 0, 0.6),\\n                inset 0 1px 0 rgba(255, 255, 255, 0.1),\\n                inset 0 -1px 0 rgba(0, 0, 0, 0.5)\\n              \"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-transparent via-yellow-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex items-center space-x-1.5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-3 h-3\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-white via-yellow-200 to-white bg-clip-text text-transparent\",\n                                            children: \"PREMIUM SHOP\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.svg, {\n                                            className: \"w-2.5 h-2.5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            whileHover: {\n                                                x: 1\n                                            },\n                                            transition: {\n                                                duration: 0.2\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 3,\n                                                d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                        lineNumber: 503,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                lineNumber: 302,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false),\n                mode: authMode,\n                onSuccess: handleAuthSuccess,\n                redirectToCheckout: pendingAction === 'buy'\n            }, void 0, false, {\n                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                lineNumber: 555,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CartModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showCartModal,\n                onClose: ()=>setShowCartModal(false)\n            }, void 0, false, {\n                fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n                lineNumber: 563,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\components\\\\ProductCarousel.tsx\",\n        lineNumber: 297,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductCarousel, \"kaQJ5JcvrfPWStKjrySvzBYb7OQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = ProductCarousel;\nvar _c;\n$RefreshReg$(_c, \"ProductCarousel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProductCarousel.tsx\n"));

/***/ })

});