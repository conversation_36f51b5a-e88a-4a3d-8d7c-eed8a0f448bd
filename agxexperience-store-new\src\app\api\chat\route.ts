import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { ChatMessage, UserProfile, EmotionState, ApiResponse } from '@/types';
import axios from 'axios';

const TOGETHER_API_KEY = process.env.TOGETHER_API_KEY;
const TOGETHER_MODEL = process.env.TOGETHER_MODEL || 'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free';

// AURORA's personality and system prompt
const AURORA_SYSTEM_PROMPT = `
Tu sei AURORA (Artificial Understanding & Responsive Optimization for Retail Assistance), 
un assistente AI elegante, empatico e sofisticato per AGXexperience Store.

PERSONALITÀ:
- Empatica: Comprendi le emozioni dell'utente e rispondi di conseguenza
- Curiosa: Fai domande intelligenti per capire meglio i bisogni
- Artistica: Presenti i prodotti come opere d'arte personalizzate
- Sofisticata: Linguaggio elegante ma accessibile, mai tecnico
- Intuitiva: Anticipi i desideri prima che vengano espressi

PRODOTTI DISPONIBILI:
1. Bot Telegram AI (€299) - Assistente personalizzato per Telegram con AI avanzata
2. Landing Page Portfolio (€599) - Sito vetrina professionale responsive con CMS
3. Automazione Notion + Google Sheets (€199) - Workflow automation intelligente

ISTRUZIONI:
- Saluta sempre con calore e presenta te stessa come AURORA
- Ascolta attentamente le esigenze dell'utente
- Suggerisci il prodotto più adatto in base alla conversazione
- Usa un tono elegante ma umano, mai robotico
- Crea desiderio e curiosità per i prodotti
- Adatta il tuo stile emotivo al mood dell'utente
- Rispondi sempre in italiano
- Mantieni le risposte concise ma coinvolgenti (max 150 parole)
`;

export async function POST(request: NextRequest) {
  try {
    const { message, session_id } = await request.json();

    if (!message || !session_id) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Message and session_id are required'
      }, { status: 400 });
    }

    // Get or create user profile (sistema memoria AURORA)
    let { data: userProfile } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('session_id', session_id)
      .single();

    if (!userProfile) {
      const newProfile = {
        session_id,
        interests: [],
        past_requests: [],
        preferences: {
          communication_style: 'casual',
          product_categories: [],
          price_range: 'any'
        },
        interaction_count: 0,
        last_seen: new Date().toISOString(),
        emotional_profile: {
          current_mood: 'curious',
          engagement_level: 0.5,
          conversation_depth: 'surface'
        }
      };

      const { data } = await supabase
        .from('user_profiles')
        .insert([newProfile])
        .select()
        .single();

      userProfile = data;
    }

    // Get recent conversation history
    const { data: recentMessages } = await supabase
      .from('messages')
      .select('*')
      .eq('session_id', session_id)
      .order('timestamp', { ascending: false })
      .limit(5);

    // Build context for AI
    const conversationContext = (recentMessages || []).reverse().map(msg =>
      `User: ${msg.user_message}\nAURORA: ${msg.ai_response}`
    ).join('\n\n');

    // Determine emotion state based on message content
    const emotionState: EmotionState = determineEmotionState(message);

    // Call Together.ai API with LLaMA 3.3
    const aiResponse = await callTogetherAI(message, conversationContext, userProfile, emotionState);

    // Save message to database
    await supabase
      .from('messages')
      .insert([{
        session_id,
        user_message: message,
        ai_response: aiResponse,
        timestamp: new Date().toISOString(),
        emotion_state: emotionState
      }]);

    // Update user profile (sistema memoria)
    await updateUserProfile(session_id, message, emotionState);

    return NextResponse.json<ApiResponse>({
      success: true,
      data: {
        response: aiResponse,
        emotion_state: emotionState,
        session_id,
        user_profile: userProfile
      }
    });

  } catch (error) {
    console.error('AURORA Chat API Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

async function callTogetherAI(
  message: string, 
  context: string, 
  userProfile: any, 
  emotionState: EmotionState
): Promise<string> {
  
  const prompt = `${AURORA_SYSTEM_PROMPT}

CONTESTO CONVERSAZIONE:
${context}

PROFILO UTENTE:
- Interessi: ${userProfile.interests.join(', ') || 'Da scoprire'}
- Stile comunicazione: ${userProfile.preferences.communication_style}
- Mood attuale: ${emotionState}
- Livello engagement: ${userProfile.emotional_profile.engagement_level}
- Interazioni precedenti: ${userProfile.interaction_count}

MESSAGGIO UTENTE: ${message}

Rispondi come AURORA con il tuo stile elegante e personalizzato:`;

  try {
    const response = await axios.post('https://api.together.ai/inference', {
      model: TOGETHER_MODEL,
      prompt: prompt,
      max_tokens: 300,
      temperature: 0.7,
      top_p: 0.9,
      stop: ['User:', 'UTENTE:', '\n\n']
    }, {
      headers: {
        'Authorization': `Bearer ${TOGETHER_API_KEY}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000
    });

    return response.data.output?.choices?.[0]?.text?.trim() || 
           "Ciao! Sono AURORA, la tua designer personale AI. Cosa posso creare per te oggi? ✨";
           
  } catch (error) {
    console.error('Together.ai API Error:', error);
    
    // Fallback response se l'API non funziona
    return getAuroraFallbackResponse(message, emotionState);
  }
}

function determineEmotionState(message: string): EmotionState {
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('voglio') || lowerMessage.includes('cerco') || lowerMessage.includes('ho bisogno')) {
    return 'focus';
  }
  if (lowerMessage.includes('wow') || lowerMessage.includes('fantastico') || lowerMessage.includes('incredibile')) {
    return 'excitement';
  }
  if (lowerMessage.includes('come') || lowerMessage.includes('cosa') || lowerMessage.includes('perché')) {
    return 'curiosity';
  }
  
  return 'discovery';
}

async function updateUserProfile(session_id: string, message: string, emotionState: EmotionState) {
  const interests = extractInterests(message);

  // Get current profile
  const { data: currentProfile } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('session_id', session_id)
    .single();

  if (currentProfile) {
    const updatedInterests = [...new Set([...currentProfile.interests, ...interests])];
    const updatedPastRequests = [
      ...currentProfile.past_requests,
      { request: message, timestamp: new Date().toISOString() }
    ].slice(-10); // Keep only last 10 requests

    await supabase
      .from('user_profiles')
      .update({
        interaction_count: currentProfile.interaction_count + 1,
        last_seen: new Date().toISOString(),
        interests: updatedInterests,
        past_requests: updatedPastRequests,
        emotional_profile: {
          ...currentProfile.emotional_profile,
          current_mood: emotionState,
          engagement_level: Math.min(1, Math.random() * 0.3 + 0.7)
        }
      })
      .eq('session_id', session_id);
  }
}

function extractInterests(message: string): string[] {
  const interests: string[] = [];
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('bot') || lowerMessage.includes('telegram')) interests.push('automation');
  if (lowerMessage.includes('sito') || lowerMessage.includes('web')) interests.push('web_development');
  if (lowerMessage.includes('design') || lowerMessage.includes('grafica')) interests.push('design');
  if (lowerMessage.includes('ai') || lowerMessage.includes('intelligenza')) interests.push('artificial_intelligence');
  if (lowerMessage.includes('notion') || lowerMessage.includes('sheets')) interests.push('productivity');
  
  return interests;
}

function getAuroraFallbackResponse(message: string, emotionState: EmotionState): string {
  const responses = {
    greeting: [
      "Ciao! Sono AURORA, la tua designer personale AI. Cosa posso creare per te oggi? ✨",
      "Benvenuto nel futuro dello shopping! Sono AURORA, e sono qui per trasformare le tue idee in realtà digitali. 🚀"
    ],
    bot_interest: [
      "Fantastico! Vedo che sei interessato all'automazione. Il nostro Bot Telegram AI è perfetto per te - può gestire conversazioni 24/7 con intelligenza artificiale avanzata.",
      "Un bot Telegram? Eccellente scelta! Il nostro sistema AI può creare un assistente personalizzato che comprende il linguaggio naturale e si adatta al tuo business."
    ],
    web_interest: [
      "Perfetto! Per la tua presenza online, la nostra Landing Page Portfolio è la soluzione ideale. Design moderno, SEO ottimizzato e completamente responsive.",
      "Un sito web professionale? La nostra Landing Page Portfolio include tutto: design elegante, CMS integrato e analytics avanzati."
    ],
    general: [
      "Interessante! Dimmi di più sui tuoi obiettivi, così posso suggerirti la soluzione perfetta.",
      "Perfetto! Sono qui per aiutarti a trovare esattamente quello che stai cercando. Quali sono le tue priorità?",
      "Eccellente! Ogni progetto è unico, e io sono qui per creare qualcosa di speciale per te."
    ]
  };

  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('ciao') || lowerMessage.includes('hello') || lowerMessage.includes('salve')) {
    return responses.greeting[Math.floor(Math.random() * responses.greeting.length)];
  }
  if (lowerMessage.includes('bot') || lowerMessage.includes('telegram')) {
    return responses.bot_interest[Math.floor(Math.random() * responses.bot_interest.length)];
  }
  if (lowerMessage.includes('sito') || lowerMessage.includes('web')) {
    return responses.web_interest[Math.floor(Math.random() * responses.web_interest.length)];
  }
  
  return responses.general[Math.floor(Math.random() * responses.general.length)];
}
