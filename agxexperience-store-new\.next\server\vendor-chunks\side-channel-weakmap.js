"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/side-channel-weakmap";
exports.ids = ["vendor-chunks/side-channel-weakmap"];
exports.modules = {

/***/ "(rsc)/./node_modules/side-channel-weakmap/index.js":
/*!****************************************************!*\
  !*** ./node_modules/side-channel-weakmap/index.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar GetIntrinsic = __webpack_require__(/*! get-intrinsic */ \"(rsc)/./node_modules/get-intrinsic/index.js\");\nvar callBound = __webpack_require__(/*! call-bound */ \"(rsc)/./node_modules/call-bound/index.js\");\nvar inspect = __webpack_require__(/*! object-inspect */ \"(rsc)/./node_modules/object-inspect/index.js\");\nvar getSideChannelMap = __webpack_require__(/*! side-channel-map */ \"(rsc)/./node_modules/side-channel-map/index.js\");\n\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(rsc)/./node_modules/es-errors/type.js\");\nvar $WeakMap = GetIntrinsic('%WeakMap%', true);\n\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => V} */\nvar $weakMapGet = callBound('WeakMap.prototype.get', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K, value: V) => void} */\nvar $weakMapSet = callBound('WeakMap.prototype.set', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => boolean} */\nvar $weakMapHas = callBound('WeakMap.prototype.has', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => boolean} */\nvar $weakMapDelete = callBound('WeakMap.prototype.delete', true);\n\n/** @type {import('.')} */\nmodule.exports = $WeakMap\n\t? /** @type {Exclude<import('.'), false>} */ function getSideChannelWeakMap() {\n\t\t/** @typedef {ReturnType<typeof getSideChannelWeakMap>} Channel */\n\t\t/** @typedef {Parameters<Channel['get']>[0]} K */\n\t\t/** @typedef {Parameters<Channel['set']>[1]} V */\n\n\t\t/** @type {WeakMap<K & object, V> | undefined} */ var $wm;\n\t\t/** @type {Channel | undefined} */ var $m;\n\n\t\t/** @type {Channel} */\n\t\tvar channel = {\n\t\t\tassert: function (key) {\n\t\t\t\tif (!channel.has(key)) {\n\t\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t\t}\n\t\t\t},\n\t\t\t'delete': function (key) {\n\t\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\t\tif ($wm) {\n\t\t\t\t\t\treturn $weakMapDelete($wm, key);\n\t\t\t\t\t}\n\t\t\t\t} else if (getSideChannelMap) {\n\t\t\t\t\tif ($m) {\n\t\t\t\t\t\treturn $m['delete'](key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\tget: function (key) {\n\t\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\t\tif ($wm) {\n\t\t\t\t\t\treturn $weakMapGet($wm, key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn $m && $m.get(key);\n\t\t\t},\n\t\t\thas: function (key) {\n\t\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\t\tif ($wm) {\n\t\t\t\t\t\treturn $weakMapHas($wm, key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn !!$m && $m.has(key);\n\t\t\t},\n\t\t\tset: function (key, value) {\n\t\t\t\tif ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n\t\t\t\t\tif (!$wm) {\n\t\t\t\t\t\t$wm = new $WeakMap();\n\t\t\t\t\t}\n\t\t\t\t\t$weakMapSet($wm, key, value);\n\t\t\t\t} else if (getSideChannelMap) {\n\t\t\t\t\tif (!$m) {\n\t\t\t\t\t\t$m = getSideChannelMap();\n\t\t\t\t\t}\n\t\t\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\t\t\t/** @type {NonNullable<typeof $m>} */ ($m).set(key, value);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\t// @ts-expect-error TODO: figure out why this is erroring\n\t\treturn channel;\n\t}\n\t: getSideChannelMap;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/side-channel-weakmap/index.js\n");

/***/ })

};
;