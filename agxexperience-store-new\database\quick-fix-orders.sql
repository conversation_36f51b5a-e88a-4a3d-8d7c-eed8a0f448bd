-- AGXexperience Store - Quick Fix for Orders Table
-- This script fixes the customer_email column issue immediately

-- Step 1: Show current state
SELECT 
    'CURRENT ORDERS TABLE STRUCTURE' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'orders'
ORDER BY ordinal_position;

-- Step 2: Fix the orders table by adding missing columns
DO $$
BEGIN
    RAISE NOTICE 'Starting orders table fix...';
    
    -- Check if orders table exists, if not create it
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders') THEN
        RAISE NOTICE 'Creating orders table...';
        CREATE TABLE orders (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            customer_email VARCHAR(255) NOT NULL DEFAULT '<EMAIL>',
            customer_name VARCHAR(255),
            total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
            status VARCHAR(50) DEFAULT 'pending',
            payment_intent_id VARCHAR(255),
            stripe_session_id VARCHAR(255),
            shipping_address JSONB,
            billing_address JSONB,
            notes TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        RAISE NOTICE 'Orders table created successfully';
    ELSE
        RAISE NOTICE 'Orders table exists, checking for missing columns...';
        
        -- Add customer_email if missing
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'orders' 
            AND column_name = 'customer_email'
        ) THEN
            ALTER TABLE orders ADD COLUMN customer_email VARCHAR(255) NOT NULL DEFAULT '<EMAIL>';
            RAISE NOTICE 'Added customer_email column';
        ELSE
            RAISE NOTICE 'customer_email column already exists';
        END IF;
        
        -- Add customer_name if missing
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'orders' 
            AND column_name = 'customer_name'
        ) THEN
            ALTER TABLE orders ADD COLUMN customer_name VARCHAR(255);
            RAISE NOTICE 'Added customer_name column';
        ELSE
            RAISE NOTICE 'customer_name column already exists';
        END IF;
        
        -- Add total_amount if missing
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'orders' 
            AND column_name = 'total_amount'
        ) THEN
            ALTER TABLE orders ADD COLUMN total_amount DECIMAL(10,2) NOT NULL DEFAULT 0;
            RAISE NOTICE 'Added total_amount column';
        ELSE
            RAISE NOTICE 'total_amount column already exists';
        END IF;
        
        -- Add status if missing
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'orders' 
            AND column_name = 'status'
        ) THEN
            ALTER TABLE orders ADD COLUMN status VARCHAR(50) DEFAULT 'pending';
            RAISE NOTICE 'Added status column';
        ELSE
            RAISE NOTICE 'status column already exists';
        END IF;
        
        -- Add other missing columns if needed
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'orders' 
            AND column_name = 'created_at'
        ) THEN
            ALTER TABLE orders ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
            RAISE NOTICE 'Added created_at column';
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'orders' 
            AND column_name = 'updated_at'
        ) THEN
            ALTER TABLE orders ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
            RAISE NOTICE 'Added updated_at column';
        END IF;
    END IF;
    
    RAISE NOTICE 'Orders table structure fix completed';
END $$;

-- Step 3: Create essential indexes
DO $$
BEGIN
    RAISE NOTICE 'Creating indexes...';
    
    CREATE INDEX IF NOT EXISTS idx_orders_customer_email ON orders(customer_email);
    CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
    CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
    
    RAISE NOTICE 'Indexes created successfully';
END $$;

-- Step 4: Enable RLS and create policy
DO $$
BEGIN
    RAISE NOTICE 'Setting up RLS and policies...';
    
    ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
    
    DROP POLICY IF EXISTS "Enable all access" ON orders;
    CREATE POLICY "Enable all access" ON orders FOR ALL USING (true);
    
    RAISE NOTICE 'RLS and policies configured';
END $$;

-- Step 5: Create order_items table if it doesn't exist
CREATE TABLE IF NOT EXISTS order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    product_id VARCHAR(255) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_price DECIMAL(10,2) NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on order_items
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Enable all access" ON order_items;
CREATE POLICY "Enable all access" ON order_items FOR ALL USING (true);

-- Create index on order_items
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);

-- Step 6: Final verification
SELECT 
    'FINAL VERIFICATION' as step,
    'Orders table structure after fix:' as info;

SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'orders'
ORDER BY ordinal_position;

-- Show success message
SELECT 
    'SUCCESS' as status, 
    'Orders table is now ready! You can run 02-sample-data.sql next.' as message;
