'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';

interface AdminPanelProps {
  isVisible: boolean;
  onClose: () => void;
}

export default function AdminPanel({ isVisible, onClose }: AdminPanelProps) {
  const router = useRouter();
  const [loading, setLoading] = useState<string | null>(null);
  const [status, setStatus] = useState<string>('');

  if (!isVisible) return null;

  // Funzioni rapide
  const quickActions = [
    {
      id: 'full-admin',
      label: 'Pagina Admin Completa',
      icon: '🏢',
      color: 'from-purple-600 to-blue-600',
      action: () => {
        router.push('/admin');
        onClose();
      }
    },
    {
      id: 'db-status',
      label: 'Status Database',
      icon: '🗄️',
      color: 'from-green-600 to-emerald-600',
      action: async () => {
        setLoading('db-status');
        try {
          const response = await fetch('/api/products');
          const data = await response.json();
          setStatus(`DB OK - ${data.data?.products?.length || 0} prodotti`);
        } catch (error) {
          setStatus('DB Error - Connessione fallita');
        } finally {
          setLoading(null);
        }
      }
    },
    {
      id: 'clear-cache',
      label: 'Clear Cache',
      icon: '🧹',
      color: 'from-orange-600 to-red-600',
      action: async () => {
        setLoading('clear-cache');
        try {
          await new Promise(resolve => setTimeout(resolve, 1000));
          setStatus('Cache cleared successfully');
        } catch (error) {
          setStatus('Cache clear failed');
        } finally {
          setLoading(null);
        }
      }
    },
    {
      id: 'restart-ai',
      label: 'Restart AI',
      icon: '🤖',
      color: 'from-cyan-600 to-blue-600',
      action: async () => {
        setLoading('restart-ai');
        try {
          await new Promise(resolve => setTimeout(resolve, 1500));
          setStatus('AI service restarted');
        } catch (error) {
          setStatus('AI restart failed');
        } finally {
          setLoading(null);
        }
      }
    },
    {
      id: 'backup-db',
      label: 'Backup DB',
      icon: '💾',
      color: 'from-indigo-600 to-purple-600',
      action: async () => {
        setLoading('backup-db');
        try {
          await new Promise(resolve => setTimeout(resolve, 2000));
          setStatus('Database backup completed');
        } catch (error) {
          setStatus('Backup failed');
        } finally {
          setLoading(null);
        }
      }
    },
    {
      id: 'system-info',
      label: 'System Info',
      icon: '📊',
      color: 'from-yellow-600 to-orange-600',
      action: () => {
        const info = {
          timestamp: new Date().toISOString(),
          memory: `${Math.round((performance as any).memory?.usedJSHeapSize / 1024 / 1024 || 0)}MB`,
          uptime: `${Math.round(performance.now() / 1000)}s`
        };
        setStatus(`System: ${info.memory} RAM, ${info.uptime} uptime`);
      }
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm"
      onClick={onClose}
    >
      <motion.div
        initial={{ y: 50 }}
        animate={{ y: 0 }}
        onClick={(e) => e.stopPropagation()}
        className="bg-gradient-to-br from-gray-900 to-black rounded-2xl p-6 border border-purple-500/30 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto"
        style={{
          background: `
            radial-gradient(circle at 20% 20%, rgba(142, 45, 226, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(0, 216, 182, 0.1) 0%, transparent 50%),
            linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)
          `
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white font-bold">⚡</span>
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Admin Panel</h2>
              <p className="text-sm text-gray-400">Funzioni rapide di sistema</p>
            </div>
          </div>
          
          <motion.button
            whileHover={{ scale: 1.1, rotate: 90 }}
            whileTap={{ scale: 0.9 }}
            onClick={onClose}
            className="w-8 h-8 bg-red-500/20 hover:bg-red-500/40 rounded-full flex items-center justify-center text-white transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </motion.button>
        </div>

        {/* Status Display */}
        {status && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-4 p-3 bg-white/5 rounded-lg border border-white/10"
          >
            <p className="text-sm text-green-300 font-mono">{status}</p>
          </motion.div>
        )}

        {/* Quick Actions Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {quickActions.map((action) => (
            <motion.button
              key={action.id}
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              onClick={action.action}
              disabled={loading === action.id}
              className={`relative p-4 rounded-xl text-white font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden bg-gradient-to-r ${action.color}`}
            >
              {/* Loading Overlay */}
              {loading === action.id && (
                <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                </div>
              )}
              
              {/* Content */}
              <div className="flex flex-col items-center space-y-2">
                <span className="text-2xl">{action.icon}</span>
                <span className="text-xs text-center leading-tight">{action.label}</span>
              </div>
              
              {/* Shimmer Effect */}
              <div className="absolute inset-0 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/20 to-transparent" />
            </motion.button>
          ))}
        </div>

        {/* Footer Info */}
        <div className="mt-6 pt-4 border-t border-white/10">
          <div className="flex items-center justify-between text-xs text-gray-400">
            <span>AGXexperience Admin v1.0</span>
            <span>{new Date().toLocaleString()}</span>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}
