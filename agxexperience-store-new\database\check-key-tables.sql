-- AGXexperience Store - Check Key Tables Structure
-- Run this to see the exact structure of the main tables

-- 1. Check orders table structure (most important)
SELECT 
    '=== ORDERS TABLE STRUCTURE ===' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'orders'
ORDER BY ordinal_position;

-- 2. Check products table structure
SELECT 
    '=== PRODUCTS TABLE STRUCTURE ===' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'products'
ORDER BY ordinal_position;

-- 3. Check user_profiles table structure
SELECT 
    '=== USER_PROFILES TABLE STRUCTURE ===' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'user_profiles'
ORDER BY ordinal_position;

-- 4. Check order_items table structure
SELECT 
    '=== ORDER_ITEMS TABLE STRUCTURE ===' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'order_items'
ORDER BY ordinal_position;

-- 5. Check current data counts
DO $$
DECLARE
    user_count INTEGER := 0;
    product_count INTEGER := 0;
    order_count INTEGER := 0;
    order_item_count INTEGER := 0;
    analytics_count INTEGER := 0;
    metrics_count INTEGER := 0;
    message_count INTEGER := 0;
BEGIN
    SELECT COUNT(*) INTO user_count FROM user_profiles;
    SELECT COUNT(*) INTO product_count FROM products;
    SELECT COUNT(*) INTO order_count FROM orders;
    SELECT COUNT(*) INTO order_item_count FROM order_items;
    SELECT COUNT(*) INTO analytics_count FROM analytics_events;
    SELECT COUNT(*) INTO metrics_count FROM system_metrics;
    SELECT COUNT(*) INTO message_count FROM messages;
    
    RAISE NOTICE '=== CURRENT DATA COUNTS ===';
    RAISE NOTICE 'User Profiles: %', user_count;
    RAISE NOTICE 'Products: %', product_count;
    RAISE NOTICE 'Orders: %', order_count;
    RAISE NOTICE 'Order Items: %', order_item_count;
    RAISE NOTICE 'Analytics Events: %', analytics_count;
    RAISE NOTICE 'System Metrics: %', metrics_count;
    RAISE NOTICE 'Messages: %', message_count;
    RAISE NOTICE '================================';
END $$;

-- 6. Check if we have the key columns we need
DO $$
BEGIN
    RAISE NOTICE '=== KEY COLUMNS CHECK ===';
    
    -- Check orders table key columns
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'orders' AND column_name = 'customer_email') THEN
        RAISE NOTICE 'ORDERS: customer_email column EXISTS';
    ELSE
        RAISE NOTICE 'ORDERS: customer_email column MISSING';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'orders' AND column_name = 'total_amount') THEN
        RAISE NOTICE 'ORDERS: total_amount column EXISTS';
    ELSE
        RAISE NOTICE 'ORDERS: total_amount column MISSING';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'orders' AND column_name = 'total') THEN
        RAISE NOTICE 'ORDERS: total column EXISTS';
    ELSE
        RAISE NOTICE 'ORDERS: total column MISSING';
    END IF;
    
    -- Check products table key columns
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'category') THEN
        RAISE NOTICE 'PRODUCTS: category column EXISTS';
    ELSE
        RAISE NOTICE 'PRODUCTS: category column MISSING';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'price') THEN
        RAISE NOTICE 'PRODUCTS: price column EXISTS';
    ELSE
        RAISE NOTICE 'PRODUCTS: price column MISSING';
    END IF;
    
    -- Check user_profiles key columns
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'session_id') THEN
        RAISE NOTICE 'USER_PROFILES: session_id column EXISTS';
    ELSE
        RAISE NOTICE 'USER_PROFILES: session_id column MISSING';
    END IF;
    
    RAISE NOTICE '================================';
END $$;

-- 7. Show sample data if exists
DO $$
DECLARE
    sample_order RECORD;
    sample_product RECORD;
BEGIN
    RAISE NOTICE '=== SAMPLE DATA CHECK ===';
    
    -- Check if orders have data
    SELECT COUNT(*) as order_count FROM orders INTO sample_order;
    IF sample_order.order_count > 0 THEN
        RAISE NOTICE 'Orders table has % existing records', sample_order.order_count;
    ELSE
        RAISE NOTICE 'Orders table is EMPTY - safe to insert sample data';
    END IF;
    
    -- Check if products have data
    SELECT COUNT(*) as product_count FROM products INTO sample_product;
    IF sample_product.product_count > 0 THEN
        RAISE NOTICE 'Products table has % existing records', sample_product.product_count;
    ELSE
        RAISE NOTICE 'Products table is EMPTY - safe to insert sample data';
    END IF;
    
    RAISE NOTICE '================================';
END $$;

SELECT 'STRUCTURE CHECK COMPLETE' as status, 'Ready to create custom insert script' as next_step;
