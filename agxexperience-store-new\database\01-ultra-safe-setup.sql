-- AGXexperience Store - Ultra Safe Setup with Column Checks
-- This script handles all conflicts and column mismatches automatically

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create update function if not exists
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Helper function to check if column exists
CREATE OR REPLACE FUNCTION column_exists(p_table_name text, p_column_name text)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = p_table_name
        AND column_name = p_column_name
    );
END;
$$ LANGUAGE plpgsql;

-- User profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) UNIQUE NOT NULL,
    email VA<PERSON>HAR(255),
    first_name VA<PERSON>HA<PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    phone VARCHAR(50),
    country VARCHAR(100),
    city VARCHAR(100),
    interests TEXT[] DEFAULT '{}',
    past_requests JSONB DEFAULT '[]',
    preferences JSONB DEFAULT '{}',
    interaction_count INTEGER DEFAULT 0,
    total_orders INTEGER DEFAULT 0,
    total_spent DECIMAL(10,2) DEFAULT 0,
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_order_date TIMESTAMP WITH TIME ZONE,
    marketing_consent BOOLEAN DEFAULT false,
    emotional_profile JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Messages table
CREATE TABLE IF NOT EXISTS messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) NOT NULL,
    user_message TEXT NOT NULL,
    ai_response TEXT NOT NULL,
    emotion_state VARCHAR(100),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    category VARCHAR(100),
    tags TEXT[] DEFAULT '{}',
    image_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Orders table with column checks
DO $$
BEGIN
    -- Create table if not exists
    CREATE TABLE IF NOT EXISTS orders (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        customer_email VARCHAR(255) NOT NULL,
        customer_name VARCHAR(255),
        total_amount DECIMAL(10,2) NOT NULL,
        status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'cancelled', 'refunded')),
        payment_intent_id VARCHAR(255),
        stripe_session_id VARCHAR(255),
        shipping_address JSONB,
        billing_address JSONB,
        notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    -- Add missing columns if they don't exist (using direct SQL checks to avoid function conflicts)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'orders'
        AND column_name = 'customer_email'
    ) THEN
        ALTER TABLE orders ADD COLUMN customer_email VARCHAR(255) NOT NULL DEFAULT '<EMAIL>';
        RAISE NOTICE 'Added customer_email column to orders table';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'orders'
        AND column_name = 'customer_name'
    ) THEN
        ALTER TABLE orders ADD COLUMN customer_name VARCHAR(255);
        RAISE NOTICE 'Added customer_name column to orders table';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'orders'
        AND column_name = 'total_amount'
    ) THEN
        ALTER TABLE orders ADD COLUMN total_amount DECIMAL(10,2) NOT NULL DEFAULT 0;
        RAISE NOTICE 'Added total_amount column to orders table';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'orders'
        AND column_name = 'status'
    ) THEN
        ALTER TABLE orders ADD COLUMN status VARCHAR(50) DEFAULT 'pending';
        RAISE NOTICE 'Added status column to orders table';
    END IF;
    
    RAISE NOTICE 'Orders table verified and updated';
END $$;

-- Order items table
CREATE TABLE IF NOT EXISTS order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    product_id VARCHAR(255) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_price DECIMAL(10,2) NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Cart table
CREATE TABLE IF NOT EXISTS cart (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) NOT NULL,
    product_id VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Analytics events table
CREATE TABLE IF NOT EXISTS analytics_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB,
    page_url VARCHAR(500),
    user_agent TEXT,
    ip_address INET,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System metrics table
CREATE TABLE IF NOT EXISTS system_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_type VARCHAR(100) NOT NULL,
    metric_value DECIMAL(10,4) NOT NULL,
    metric_unit VARCHAR(50),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes safely with column existence checks
DO $$
BEGIN
    -- User profiles indexes
    CREATE INDEX IF NOT EXISTS idx_user_profiles_session_id ON user_profiles(session_id);
    CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
    CREATE INDEX IF NOT EXISTS idx_user_profiles_last_seen ON user_profiles(last_seen);
    
    -- Messages indexes
    CREATE INDEX IF NOT EXISTS idx_messages_session_id ON messages(session_id);
    
    -- Orders indexes (with column checks)
    CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
    CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
    
    -- Only create customer_email index if column exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'orders'
        AND column_name = 'customer_email'
    ) THEN
        CREATE INDEX IF NOT EXISTS idx_orders_customer_email ON orders(customer_email);
        RAISE NOTICE 'Created customer_email index';
    ELSE
        RAISE NOTICE 'Skipped customer_email index - column does not exist';
    END IF;
    
    -- Order items indexes
    CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
    CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);
    
    -- Analytics indexes
    CREATE INDEX IF NOT EXISTS idx_analytics_events_session_id ON analytics_events(session_id);
    CREATE INDEX IF NOT EXISTS idx_analytics_events_event_type ON analytics_events(event_type);
    CREATE INDEX IF NOT EXISTS idx_analytics_events_created_at ON analytics_events(created_at);
    
    -- System metrics indexes
    CREATE INDEX IF NOT EXISTS idx_system_metrics_type ON system_metrics(metric_type);
    CREATE INDEX IF NOT EXISTS idx_system_metrics_created_at ON system_metrics(created_at);
    
    RAISE NOTICE 'All indexes created successfully';
END $$;

-- Enable RLS safely
DO $$
BEGIN
    ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
    ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
    ALTER TABLE products ENABLE ROW LEVEL SECURITY;
    ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
    ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
    ALTER TABLE cart ENABLE ROW LEVEL SECURITY;
    ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;
    ALTER TABLE system_metrics ENABLE ROW LEVEL SECURITY;
    
    RAISE NOTICE 'RLS enabled on all tables';
END $$;

-- Create policies safely
DO $$
BEGIN
    -- Drop and recreate policies to avoid conflicts
    DROP POLICY IF EXISTS "Enable all access" ON user_profiles;
    CREATE POLICY "Enable all access" ON user_profiles FOR ALL USING (true);
    
    DROP POLICY IF EXISTS "Enable all access" ON messages;
    CREATE POLICY "Enable all access" ON messages FOR ALL USING (true);
    
    DROP POLICY IF EXISTS "Enable all access" ON products;
    CREATE POLICY "Enable all access" ON products FOR ALL USING (true);
    
    DROP POLICY IF EXISTS "Enable all access" ON orders;
    CREATE POLICY "Enable all access" ON orders FOR ALL USING (true);
    
    DROP POLICY IF EXISTS "Enable all access" ON order_items;
    CREATE POLICY "Enable all access" ON order_items FOR ALL USING (true);
    
    DROP POLICY IF EXISTS "Enable all access" ON cart;
    CREATE POLICY "Enable all access" ON cart FOR ALL USING (true);
    
    DROP POLICY IF EXISTS "Enable all access" ON analytics_events;
    CREATE POLICY "Enable all access" ON analytics_events FOR ALL USING (true);
    
    DROP POLICY IF EXISTS "Enable all access" ON system_metrics;
    CREATE POLICY "Enable all access" ON system_metrics FOR ALL USING (true);
    
    RAISE NOTICE 'All policies created successfully';
END $$;

-- Create triggers safely
DO $$
BEGIN
    DROP TRIGGER IF EXISTS update_orders_updated_at ON orders;
    CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
    CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    DROP TRIGGER IF EXISTS update_products_updated_at ON products;
    CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    DROP TRIGGER IF EXISTS update_cart_updated_at ON cart;
    CREATE TRIGGER update_cart_updated_at BEFORE UPDATE ON cart
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    
    RAISE NOTICE 'All triggers created successfully';
END $$;

-- Grant permissions
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Clean up helper function
DROP FUNCTION IF EXISTS column_exists(text, text);
DROP FUNCTION IF EXISTS column_exists(p_table_name text, p_column_name text);

-- Final verification with detailed column check
SELECT 
    'Setup completed successfully!' as status,
    COUNT(*) as tables_created,
    string_agg(table_name, ', ') as tables_list
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('user_profiles', 'messages', 'products', 'orders', 'order_items', 'cart', 'analytics_events', 'system_metrics');

-- Show orders table structure for verification
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'orders'
ORDER BY ordinal_position;
