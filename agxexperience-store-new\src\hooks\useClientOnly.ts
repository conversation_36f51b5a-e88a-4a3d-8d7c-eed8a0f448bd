import { useState, useEffect } from 'react';

export function useClientOnly() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

export function useRandomParticles(count: number = 10) {
  const [particles, setParticles] = useState<Array<{ left: string; top: string; duration: number; delay: number }>>([]);
  const isClient = useClientOnly();

  useEffect(() => {
    if (isClient) {
      const newParticles = Array.from({ length: count }, () => ({
        left: `${Math.random() * 100}%`,
        top: `${Math.random() * 100}%`,
        duration: 2 + Math.random() * 3,
        delay: Math.random() * 2,
      }));
      setParticles(newParticles);
    }
  }, [isClient, count]);

  return { particles, isClient };
}
