-- AGXexperience Store - Fix Customer Email Column Issue
-- Run this to fix the specific customer_email column problem

-- Step 1: Check current state
SELECT 
    'DIAGNOSIS' as step,
    table_name,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'orders'
ORDER BY ordinal_position;

-- Step 2: Fix the orders table structure
DO $$
BEGIN
    -- Check if orders table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders') THEN
        RAISE NOTICE 'Orders table exists, checking columns...';
        
        -- Add customer_email if missing
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'orders' 
            AND column_name = 'customer_email'
        ) THEN
            ALTER TABLE orders ADD COLUMN customer_email VARCHAR(255) NOT NULL DEFAULT '<EMAIL>';
            RAISE NOTICE 'Added customer_email column';
        ELSE
            RAISE NOTICE 'customer_email column already exists';
        END IF;
        
        -- Add customer_name if missing
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'orders' 
            AND column_name = 'customer_name'
        ) THEN
            ALTER TABLE orders ADD COLUMN customer_name VARCHAR(255);
            RAISE NOTICE 'Added customer_name column';
        ELSE
            RAISE NOTICE 'customer_name column already exists';
        END IF;
        
        -- Add total_amount if missing
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'orders' 
            AND column_name = 'total_amount'
        ) THEN
            ALTER TABLE orders ADD COLUMN total_amount DECIMAL(10,2) NOT NULL DEFAULT 0;
            RAISE NOTICE 'Added total_amount column';
        ELSE
            RAISE NOTICE 'total_amount column already exists';
        END IF;
        
        -- Add status if missing
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'orders' 
            AND column_name = 'status'
        ) THEN
            ALTER TABLE orders ADD COLUMN status VARCHAR(50) DEFAULT 'pending';
            RAISE NOTICE 'Added status column';
        ELSE
            RAISE NOTICE 'status column already exists';
        END IF;
        
    ELSE
        RAISE NOTICE 'Orders table does not exist, creating it...';
        
        -- Create the complete orders table
        CREATE TABLE orders (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            customer_email VARCHAR(255) NOT NULL,
            customer_name VARCHAR(255),
            total_amount DECIMAL(10,2) NOT NULL,
            status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'cancelled', 'refunded')),
            payment_intent_id VARCHAR(255),
            stripe_session_id VARCHAR(255),
            shipping_address JSONB,
            billing_address JSONB,
            notes TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        RAISE NOTICE 'Created orders table with all columns';
    END IF;
END $$;

-- Step 3: Create indexes safely
DO $$
BEGIN
    -- Create indexes only if columns exist
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'orders' 
        AND column_name = 'customer_email'
    ) THEN
        CREATE INDEX IF NOT EXISTS idx_orders_customer_email ON orders(customer_email);
        RAISE NOTICE 'Created customer_email index';
    END IF;
    
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'orders' 
        AND column_name = 'status'
    ) THEN
        CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
        RAISE NOTICE 'Created status index';
    END IF;
    
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'orders' 
        AND column_name = 'created_at'
    ) THEN
        CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
        RAISE NOTICE 'Created created_at index';
    END IF;
END $$;

-- Step 4: Enable RLS and create policy
DO $$
BEGIN
    ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
    
    DROP POLICY IF EXISTS "Enable all access" ON orders;
    CREATE POLICY "Enable all access" ON orders FOR ALL USING (true);
    
    RAISE NOTICE 'RLS and policies configured for orders table';
END $$;

-- Step 5: Verification
SELECT 
    'VERIFICATION' as step,
    'Orders table structure after fix:' as info;

SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'orders'
ORDER BY ordinal_position;

-- Step 6: Show indexes
SELECT 
    'INDEXES' as step,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'orders' 
AND schemaname = 'public';

SELECT 'FIX COMPLETED' as status, 'Orders table is now ready for use' as message;
