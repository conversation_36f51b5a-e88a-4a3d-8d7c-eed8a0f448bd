-- AGXexperience Store - Safe Data Insert (handles duplicates)
-- This script safely inserts data without conflicts

-- 1. Insert sample user profiles (ON CONFLICT DO NOTHING)
INSERT INTO user_profiles (
    session_id, email, first_name, last_name, interaction_count, 
    interests, preferences, emotional_profile, 
    created_at, last_seen
) VALUES
('session_1753401234567_abc123', '<EMAIL>', '<PERSON>', '<PERSON>', 25, 
 ARRAY['AI Tools', 'Automation'], 
 '{"communication_style": "professional", "product_categories": ["AI Tools", "Automation"], "price_range": "premium"}', 
 '{"current_mood": "curious", "engagement_level": 0.8, "conversation_depth": "deep"}', 
 NOW() - INTERVAL '1 week', NOW() - INTERVAL '30 minutes'),

('session_1753401234568_def456', '<EMAIL>', 'Giulia', '<PERSON><PERSON>chi', 18, 
 ARRAY['E-commerce', 'Marketing'], 
 '{"communication_style": "casual", "product_categories": ["E-commerce", "Marketing"], "price_range": "medium"}', 
 '{"current_mood": "excited", "engagement_level": 0.9, "conversation_depth": "medium"}', 
 NOW() - INTERVAL '2 weeks', NOW() - INTERVAL '1 hour'),

('session_1753401234569_ghi789', '<EMAIL>', 'Luca', 'Verdi', 42, 
 ARRAY['AI Tools', 'Analytics'], 
 '{"communication_style": "technical", "product_categories": ["AI Tools", "Analytics"], "price_range": "premium"}', 
 '{"current_mood": "focused", "engagement_level": 0.7, "conversation_depth": "deep"}', 
 NOW() - INTERVAL '1 month', NOW() - INTERVAL '2 hours'),

('session_1753401234570_jkl012', '<EMAIL>', 'Anna', 'Ferrari', 12, 
 ARRAY['Social Media', 'Content'], 
 '{"communication_style": "friendly", "product_categories": ["Social Media", "Content"], "price_range": "budget"}', 
 '{"current_mood": "interested", "engagement_level": 0.6, "conversation_depth": "surface"}', 
 NOW() - INTERVAL '5 days', NOW() - INTERVAL '1 day'),

('session_1753401234571_mno345', '<EMAIL>', 'Paolo', 'Russo', 35, 
 ARRAY['Automation', 'Analytics'], 
 '{"communication_style": "professional", "product_categories": ["Automation", "Analytics"], "price_range": "premium"}', 
 '{"current_mood": "analytical", "engagement_level": 0.8, "conversation_depth": "deep"}', 
 NOW() - INTERVAL '3 weeks', NOW() - INTERVAL '30 minutes')
ON CONFLICT (session_id) DO NOTHING;

-- 2. Insert sample products (ON CONFLICT DO NOTHING)
INSERT INTO products (id, name, description, price, category, tags, is_active) VALUES
('ai-content-generator', 'AI Content Generator', 'Genera contenuti di alta qualità con intelligenza artificiale avanzata', 299.00, 'AI Tools', ARRAY['AI', 'Content', 'Writing'], true),
('bot-telegram-ai', 'Bot Telegram AI Premium', 'Bot Telegram intelligente per automazione e customer service', 599.00, 'Automation', ARRAY['Bot', 'Telegram', 'AI'], true),
('ecommerce-setup', 'E-commerce Store Setup', 'Setup completo negozio e-commerce con AI integrata', 199.00, 'E-commerce', ARRAY['E-commerce', 'Setup', 'Store'], true),
('social-media-ai', 'Social Media Manager AI', 'Gestione automatica social media con AI', 399.00, 'Marketing', ARRAY['Social Media', 'AI', 'Marketing'], true),
('ai-analytics', 'AI Analytics Dashboard', 'Dashboard analytics avanzata con insights AI', 799.00, 'Analytics', ARRAY['Analytics', 'AI', 'Dashboard'], true),
('seo-optimizer', 'SEO Optimizer AI', 'Ottimizzazione SEO automatica con intelligenza artificiale', 149.00, 'Marketing', ARRAY['SEO', 'AI', 'Optimization'], true),
('ai-image-generator', 'AI Image Generator', 'Generatore di immagini AI per contenuti creativi', 249.00, 'AI Tools', ARRAY['AI', 'Images', 'Creative'], true),
('complete-ai-suite', 'Complete AI Suite', 'Suite completa di strumenti AI per business', 899.00, 'AI Tools', ARRAY['AI', 'Suite', 'Business'], true)
ON CONFLICT (id) DO NOTHING;

-- 3. Clear existing orders and order_items to avoid conflicts
DELETE FROM order_items;
DELETE FROM orders;

-- 4. Insert sample orders (fresh insert after clearing)
INSERT INTO orders (session_id, customer_email, customer_name, total_amount, total, status, created_at) VALUES
('session_1753401234567_abc123', '<EMAIL>', 'Marco Rossi', 299.00, 299.00, 'completed', NOW() - INTERVAL '1 day'),
('session_1753401234568_def456', '<EMAIL>', 'Giulia Bianchi', 599.00, 599.00, 'processing', NOW() - INTERVAL '2 days'),
('session_1753401234569_ghi789', '<EMAIL>', 'Luca Verdi', 199.00, 199.00, 'completed', NOW() - INTERVAL '3 days'),
('session_1753401234570_jkl012', '<EMAIL>', 'Anna Ferrari', 399.00, 399.00, 'pending', NOW() - INTERVAL '4 days'),
('session_1753401234571_mno345', '<EMAIL>', 'Paolo Russo', 799.00, 799.00, 'completed', NOW() - INTERVAL '5 days'),
('session_1753401234572_pqr678', '<EMAIL>', 'Sara Marino', 149.00, 149.00, 'processing', NOW() - INTERVAL '6 days'),
('session_1753401234573_stu901', '<EMAIL>', 'Davide Costa', 899.00, 899.00, 'completed', NOW() - INTERVAL '7 days'),
('session_1753401234574_vwx234', '<EMAIL>', 'Elena Ricci', 249.00, 249.00, 'cancelled', NOW() - INTERVAL '8 days'),
('session_1753401234575_yza567', '<EMAIL>', 'Francesco Bruno', 599.00, 599.00, 'completed', NOW() - INTERVAL '10 days'),
('session_1753401234576_bcd890', '<EMAIL>', 'Chiara Galli', 399.00, 399.00, 'completed', NOW() - INTERVAL '12 days');

-- 5. Insert order items (linking to orders)
DO $$
DECLARE
    order_record RECORD;
BEGIN
    -- Get orders and insert corresponding order items
    FOR order_record IN 
        SELECT id, customer_email, total_amount FROM orders 
    LOOP
        CASE 
            WHEN order_record.customer_email = '<EMAIL>' THEN
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'ai-content-generator', 'AI Content Generator', 299.00, 1, 299.00);
            WHEN order_record.customer_email = '<EMAIL>' THEN
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'bot-telegram-ai', 'Bot Telegram AI Premium', 599.00, 1, 599.00);
            WHEN order_record.customer_email = '<EMAIL>' THEN
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'ecommerce-setup', 'E-commerce Store Setup', 199.00, 1, 199.00);
            WHEN order_record.customer_email = '<EMAIL>' THEN
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'social-media-ai', 'Social Media Manager AI', 399.00, 1, 399.00);
            WHEN order_record.customer_email = '<EMAIL>' THEN
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'ai-analytics', 'AI Analytics Dashboard', 799.00, 1, 799.00);
            WHEN order_record.customer_email = '<EMAIL>' THEN
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'seo-optimizer', 'SEO Optimizer AI', 149.00, 1, 149.00);
            WHEN order_record.customer_email = '<EMAIL>' THEN
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'complete-ai-suite', 'Complete AI Suite', 899.00, 1, 899.00);
            WHEN order_record.customer_email = '<EMAIL>' THEN
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'ai-image-generator', 'AI Image Generator', 249.00, 1, 249.00);
            ELSE
                -- Default case for other orders
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'ai-content-generator', 'AI Content Generator', 299.00, 1, order_record.total_amount);
        END CASE;
    END LOOP;
    
    RAISE NOTICE 'Order items inserted successfully';
END $$;

-- 6. Clear and insert analytics events
DELETE FROM analytics_events;
INSERT INTO analytics_events (session_id, event_type, event_data, page_url, created_at) VALUES
('session_1753401234567_abc123', 'page_view', '{"page": "homepage"}', '/', NOW() - INTERVAL '1 hour'),
('session_1753401234567_abc123', 'product_view', '{"product_id": "ai-content-generator"}', '/products/ai-content-generator', NOW() - INTERVAL '50 minutes'),
('session_1753401234567_abc123', 'add_to_cart', '{"product_id": "ai-content-generator", "quantity": 1}', '/shop', NOW() - INTERVAL '45 minutes'),
('session_1753401234568_def456', 'page_view', '{"page": "shop"}', '/shop', NOW() - INTERVAL '2 hours'),
('session_1753401234568_def456', 'product_view', '{"product_id": "bot-telegram-ai"}', '/products/bot-telegram-ai', NOW() - INTERVAL '1 hour 30 minutes'),
('session_1753401234569_ghi789', 'page_view', '{"page": "homepage"}', '/', NOW() - INTERVAL '3 hours'),
('session_1753401234569_ghi789', 'chat_interaction', '{"message_count": 5}', '/', NOW() - INTERVAL '2 hours 45 minutes'),
('session_1753401234570_jkl012', 'page_view', '{"page": "shop"}', '/shop', NOW() - INTERVAL '1 day'),
('session_1753401234571_mno345', 'product_view', '{"product_id": "ai-analytics"}', '/products/ai-analytics', NOW() - INTERVAL '45 minutes');

-- 7. Clear and insert system metrics
DELETE FROM system_metrics;
INSERT INTO system_metrics (metric_type, metric_value, metric_unit, created_at) VALUES
('cpu_usage', 25.5, 'percent', NOW() - INTERVAL '5 minutes'),
('memory_usage', 68.2, 'percent', NOW() - INTERVAL '5 minutes'),
('disk_usage', 45.8, 'percent', NOW() - INTERVAL '5 minutes'),
('response_time', 245, 'milliseconds', NOW() - INTERVAL '5 minutes'),
('active_connections', 12, 'count', NOW() - INTERVAL '5 minutes'),
('cpu_usage', 23.1, 'percent', NOW() - INTERVAL '10 minutes'),
('memory_usage', 66.8, 'percent', NOW() - INTERVAL '10 minutes'),
('response_time', 198, 'milliseconds', NOW() - INTERVAL '10 minutes');

-- 8. Clear and insert sample messages
DELETE FROM messages;
INSERT INTO messages (session_id, user_message, ai_response, emotion_state, timestamp) VALUES
('session_1753401234567_abc123', 'Ciao, mi puoi aiutare con i prodotti AI?', 'Ciao! Sono AURORA, la tua assistente AI. Sarò felice di aiutarti a scoprire i nostri prodotti AI. Cosa ti interessa di più?', 'curious', NOW() - INTERVAL '1 hour'),
('session_1753401234567_abc123', 'Vorrei qualcosa per generare contenuti', 'Perfetto! Il nostro AI Content Generator è ideale per te. Genera contenuti di alta qualità per blog, social media e marketing. Vuoi saperne di più?', 'excited', NOW() - INTERVAL '55 minutes'),
('session_1753401234568_def456', 'Hai bot per Telegram?', 'Sì! Abbiamo il Bot Telegram AI Premium, perfetto per automazione e customer service. È molto popolare tra i nostri clienti business.', 'helpful', NOW() - INTERVAL '2 hours'),
('session_1753401234569_ghi789', 'Cerco soluzioni analytics avanzate', 'Ottima scelta! La nostra AI Analytics Dashboard offre insights avanzati con intelligenza artificiale. Perfetta per analisi approfondite dei dati.', 'professional', NOW() - INTERVAL '3 hours');

-- 9. Final verification and summary
DO $$
DECLARE
    user_count INTEGER;
    product_count INTEGER;
    order_count INTEGER;
    order_item_count INTEGER;
    analytics_count INTEGER;
    metrics_count INTEGER;
    message_count INTEGER;
    total_revenue NUMERIC;
BEGIN
    SELECT COUNT(*) INTO user_count FROM user_profiles;
    SELECT COUNT(*) INTO product_count FROM products;
    SELECT COUNT(*) INTO order_count FROM orders;
    SELECT COUNT(*) INTO order_item_count FROM order_items;
    SELECT COUNT(*) INTO analytics_count FROM analytics_events;
    SELECT COUNT(*) INTO metrics_count FROM system_metrics;
    SELECT COUNT(*) INTO message_count FROM messages;
    SELECT SUM(total_amount) INTO total_revenue FROM orders WHERE status = 'completed';
    
    RAISE NOTICE '=== SAFE DATA INSERTION COMPLETE ===';
    RAISE NOTICE 'User Profiles: %', user_count;
    RAISE NOTICE 'Products: %', product_count;
    RAISE NOTICE 'Orders: %', order_count;
    RAISE NOTICE 'Order Items: %', order_item_count;
    RAISE NOTICE 'Analytics Events: %', analytics_count;
    RAISE NOTICE 'System Metrics: %', metrics_count;
    RAISE NOTICE 'Messages: %', message_count;
    RAISE NOTICE 'Total Revenue (completed orders): €%', total_revenue;
    RAISE NOTICE '========================================';
    RAISE NOTICE 'Dashboard is ready! Go to http://localhost:3000/admin';
END $$;
