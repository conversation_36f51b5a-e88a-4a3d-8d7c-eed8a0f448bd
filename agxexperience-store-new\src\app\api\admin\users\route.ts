import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

// Helper functions
function calculateAverageSessionDuration(users: any[]): number {
  // This would require session start/end tracking
  // For now, return a placeholder based on interaction count
  const totalInteractions = users.reduce((sum, user) => sum + (user.interaction_count || 0), 0);
  return users.length > 0 ? Math.round((totalInteractions * 2.5) / users.length) : 0; // Estimated minutes
}

function calculateMostActiveHour(users: any[]): number {
  const hourCounts = new Array(24).fill(0);

  users.forEach(user => {
    if (user.last_seen) {
      const hour = new Date(user.last_seen).getHours();
      hourCounts[hour]++;
    }
  });

  const maxCount = Math.max(...hourCounts);
  return hourCounts.indexOf(maxCount);
}

export async function GET(request: NextRequest) {
  try {
    // Fetch real users from user_profiles table
    const { data: users, error } = await supabaseAdmin
      .from('user_profiles')
      .select('*')
      .order('last_seen', { ascending: false });

    if (error) {
      console.error('Error fetching users:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch users from database' },
        { status: 500 }
      );
    }

    // Use the fetched users data
    const usersData = users || [];

    // Also fetch related analytics data for better insights (only if we have users)
    let analyticsEvents = null;
    if (usersData.length > 0) {
      const { data } = await supabaseAdmin
        .from('analytics_events')
        .select('session_id, event_type, created_at')
        .in('session_id', usersData.map(user => user.session_id))
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());
      analyticsEvents = data;
    }

    // Enrich users data with analytics
    const enrichedUsers = usersData.map(user => {
      const userEvents = analyticsEvents?.filter(event => event.session_id === user.session_id) || [];
      return {
        ...user,
        recent_activity_count: userEvents.length,
        last_activity_type: userEvents[0]?.event_type || 'unknown'
      };
    });

    // Calculate real user statistics
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const stats = {
      total: enrichedUsers.length,
      activeLastHour: enrichedUsers.filter(user =>
        user.last_seen && new Date(user.last_seen) > oneHourAgo
      ).length,
      activeToday: enrichedUsers.filter(user =>
        user.last_seen && new Date(user.last_seen) > oneDayAgo
      ).length,
      newThisWeek: enrichedUsers.filter(user =>
        user.created_at && new Date(user.created_at) > oneWeekAgo
      ).length,
      avgInteractions: enrichedUsers.length > 0
        ? enrichedUsers.reduce((sum, user) => sum + (user.interaction_count || 0), 0) / enrichedUsers.length
        : 0,
      topEngaged: enrichedUsers
        .filter(user => user.interaction_count > 0)
        .sort((a, b) => (b.interaction_count || 0) - (a.interaction_count || 0))
        .slice(0, 5)
        .map(user => ({
          id: user.id,
          email: user.email || 'N/A',
          interactions: user.interaction_count || 0,
          session_id: user.session_id
        })),
      // Additional real metrics
      totalSessions: new Set(enrichedUsers.map(user => user.session_id)).size,
      usersWithEmail: enrichedUsers.filter(user => user.email).length,
      averageSessionDuration: calculateAverageSessionDuration(enrichedUsers),
      mostActiveHour: calculateMostActiveHour(enrichedUsers)
    };

    return NextResponse.json({
      success: true,
      data: enrichedUsers,
      stats,
      meta: {
        total_records: enrichedUsers.length,
        last_updated: new Date().toISOString(),
        data_source: 'supabase'
      }
    });

  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { userId, updates } = await request.json();

    // Update user in database
    const { data, error } = await supabaseAdmin
      .from('user_profiles')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', userId)
      .select();

    if (error) throw error;

    return NextResponse.json({
      success: true,
      data: data[0],
      message: 'User updated successfully'
    });

  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update user' },
      { status: 500 }
    );
  }
}
