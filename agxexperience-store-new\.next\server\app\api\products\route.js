/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/products/route";
exports.ids = ["app/api/products/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var G_AGtech_E_commerce_agxexperience_store_new_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/products/route.ts */ \"(rsc)/./src/app/api/products/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/products/route\",\n        pathname: \"/api/products\",\n        filename: \"route\",\n        bundlePath: \"app/api/products/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\app\\\\api\\\\products\\\\route.ts\",\n    nextConfigOutput,\n    userland: G_AGtech_E_commerce_agxexperience_store_new_src_app_api_products_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/products/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/products/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/products/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\n// 🔄 Mock data for development when MongoDB is not available\nconst mockProducts = [\n    {\n        _id: '1',\n        name: 'Bot Telegram AI Premium',\n        description: 'Bot Telegram personalizzato con intelligenza artificiale avanzata per automatizzare le tue conversazioni e gestire il customer service.',\n        price: 299.99,\n        slug: 'bot-telegram-ai-premium',\n        tags: [\n            'telegram',\n            'ai',\n            'automation',\n            'premium'\n        ],\n        features: [\n            'AI Conversazionale',\n            'Integrazione API',\n            'Dashboard Analytics',\n            'Support 24/7'\n        ],\n        created_at: new Date(),\n        updated_at: new Date()\n    },\n    {\n        _id: '2',\n        name: 'Portfolio Website Futuristico',\n        description: 'Sito web portfolio con design futuristico, animazioni 3D e integrazione AI per mostrare i tuoi progetti in modo innovativo.',\n        price: 599.99,\n        slug: 'portfolio-website-futuristico',\n        tags: [\n            'portfolio',\n            'website',\n            'design',\n            '3d'\n        ],\n        features: [\n            'Design 3D',\n            'Animazioni Avanzate',\n            'SEO Ottimizzato',\n            'Mobile Responsive'\n        ],\n        created_at: new Date(),\n        updated_at: new Date()\n    },\n    {\n        _id: '3',\n        name: 'Template Notion Pro',\n        description: 'Template Notion professionale per organizzare progetti, task e knowledge base con sistema di automazione integrato.',\n        price: 49.99,\n        slug: 'template-notion-pro',\n        tags: [\n            'notion',\n            'productivity',\n            'template',\n            'organization'\n        ],\n        features: [\n            'Dashboard Completa',\n            'Automazioni',\n            'Template Multipli',\n            'Guida Setup'\n        ],\n        created_at: new Date(),\n        updated_at: new Date()\n    },\n    {\n        _id: '4',\n        name: 'Sistema Automazione Business',\n        description: 'Suite completa di automazioni per il tuo business: email marketing, social media, CRM e analytics integrati.',\n        price: 899.99,\n        slug: 'sistema-automazione-business',\n        tags: [\n            'automation',\n            'business',\n            'marketing',\n            'crm'\n        ],\n        features: [\n            'Email Marketing',\n            'Social Automation',\n            'CRM Integrato',\n            'Analytics Avanzati'\n        ],\n        created_at: new Date(),\n        updated_at: new Date()\n    }\n];\n// GET - Fetch products with filters\nasync function GET(request) {\n    // Extract parameters outside try-catch for error handling access\n    const { searchParams } = new URL(request.url);\n    const category = searchParams.get('category');\n    const search = searchParams.get('search');\n    const minPrice = searchParams.get('minPrice');\n    const maxPrice = searchParams.get('maxPrice');\n    const tags = searchParams.get('tags');\n    const sortBy = searchParams.get('sortBy') || 'created_at';\n    const sortOrder = searchParams.get('sortOrder') || 'desc';\n    try {\n        // Build Supabase query\n        let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.TABLES.PRODUCTS).select('*');\n        // Category filter\n        if (category && category !== 'all') {\n            query = query.contains('tags', [\n                category\n            ]);\n        }\n        // Search filter\n        if (search) {\n            query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);\n        }\n        // Price filter\n        if (minPrice) {\n            query = query.gte('price', parseFloat(minPrice));\n        }\n        if (maxPrice) {\n            query = query.lte('price', parseFloat(maxPrice));\n        }\n        // Tags filter\n        if (tags) {\n            const tagArray = tags.split(',').map((tag)=>tag.trim());\n            query = query.overlaps('tags', tagArray);\n        }\n        // Sort configuration\n        const ascending = sortOrder === 'asc';\n        query = query.order(sortBy, {\n            ascending\n        });\n        const { data: products, error } = await query;\n        if (error) {\n            throw error;\n        }\n        // Get all unique categories (tags) for filter options\n        const { data: allProducts } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.TABLES.PRODUCTS).select('tags');\n        const allTags = [\n            ...new Set(allProducts?.flatMap((p)=>p.tags) || [])\n        ];\n        const categories = allTags.sort();\n        // Get price range from Supabase\n        const { data: priceData } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.TABLES.PRODUCTS).select('price').order('price', {\n            ascending: true\n        });\n        const prices = priceData?.map((p)=>p.price) || [];\n        const priceRange = [\n            {\n                minPrice: prices.length > 0 ? Math.min(...prices) : 0,\n                maxPrice: prices.length > 0 ? Math.max(...prices) : 1000\n            }\n        ];\n        // Transform Supabase products to include compatibility fields\n        const transformedProducts = (products || []).map((product)=>({\n                ...product,\n                _id: product.id,\n                slug: product.slug || product.id,\n                tags: product.tags || [],\n                features: product.features || [],\n                image_url: product.image_url || '',\n                created_at: new Date(product.created_at),\n                updated_at: new Date(product.updated_at || product.created_at)\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                products: transformedProducts,\n                categories,\n                priceRange: priceRange[0] ? {\n                    min: priceRange[0].minPrice,\n                    max: priceRange[0].maxPrice\n                } : {\n                    min: 0,\n                    max: 1000\n                },\n                totalCount: transformedProducts.length,\n                filters: {\n                    category,\n                    search,\n                    minPrice,\n                    maxPrice,\n                    tags,\n                    sortBy,\n                    sortOrder\n                }\n            },\n            message: `Found ${transformedProducts.length} products`\n        });\n    } catch (error) {\n        console.error('Products GET Error:', error);\n        // Check if it's a MongoDB SSL error\n        if (error instanceof Error && error.message.includes('SSL')) {\n            console.log('🔧 MongoDB SSL Error detected - Using mock data for development...');\n        } else {\n            console.log('🔄 Database connection failed - Using mock data for development...');\n        }\n        // Apply filters to mock data\n        let filteredProducts = [\n            ...mockProducts\n        ];\n        // Category filter\n        if (category && category !== 'all') {\n            filteredProducts = filteredProducts.filter((p)=>p.tags.includes(category));\n        }\n        // Search filter\n        if (search) {\n            const searchLower = search.toLowerCase();\n            filteredProducts = filteredProducts.filter((p)=>p.name.toLowerCase().includes(searchLower) || p.description.toLowerCase().includes(searchLower) || p.tags.some((tag)=>tag.toLowerCase().includes(searchLower)));\n        }\n        // Return mock data when MongoDB is not available\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                products: filteredProducts,\n                total: filteredProducts.length,\n                page: 1,\n                limit: filteredProducts.length\n            },\n            message: 'Using development mock data - MongoDB connection failed'\n        });\n    }\n}\n// POST - Create new product\nasync function POST(request) {\n    try {\n        const productData = await request.json();\n        // Validate required fields\n        const { name, description, price, slug, tags } = productData;\n        if (!name || !description || !price || !slug || !tags?.length) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Missing required fields: name, description, price, slug, tags'\n            }, {\n                status: 400\n            });\n        }\n        const db = await getDatabase();\n        // Check if slug already exists\n        const existingProduct = await db.collection(COLLECTIONS.PRODUCTS).findOne({\n            slug\n        });\n        if (existingProduct) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Product with this slug already exists'\n            }, {\n                status: 409\n            });\n        }\n        // Create new product\n        const newProduct = {\n            name,\n            description,\n            price: Number(price),\n            slug,\n            image_url: productData.image_url || '',\n            tags: Array.isArray(tags) ? tags : [],\n            features: Array.isArray(productData.features) ? productData.features : [],\n            created_at: new Date(),\n            updated_at: new Date()\n        };\n        const result = await db.collection(COLLECTIONS.PRODUCTS).insertOne(newProduct);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                ...newProduct,\n                _id: result.insertedId.toString()\n            },\n            message: 'Product created successfully'\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Products POST Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to create product'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - Update existing product\nasync function PUT(request) {\n    try {\n        const productData = await request.json();\n        const { _id, ...updateData } = productData;\n        if (!_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Product ID is required'\n            }, {\n                status: 400\n            });\n        }\n        const db = await getDatabase();\n        // Check if product exists\n        const existingProduct = await db.collection(COLLECTIONS.PRODUCTS).findOne({\n            _id\n        });\n        if (!existingProduct) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Product not found'\n            }, {\n                status: 404\n            });\n        }\n        // Update product\n        const updatedProduct = {\n            ...updateData,\n            price: Number(updateData.price),\n            updated_at: new Date()\n        };\n        await db.collection(COLLECTIONS.PRODUCTS).updateOne({\n            _id\n        }, {\n            $set: updatedProduct\n        });\n        const result = await db.collection(COLLECTIONS.PRODUCTS).findOne({\n            _id\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: result,\n            message: 'Product updated successfully'\n        });\n    } catch (error) {\n        console.error('Products PUT Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to update product'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - Delete product\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const productId = searchParams.get('id');\n        if (!productId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Product ID is required'\n            }, {\n                status: 400\n            });\n        }\n        const db = await getDatabase();\n        const result = await db.collection(COLLECTIONS.PRODUCTS).deleteOne({\n            _id: productId\n        });\n        if (result.deletedCount === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Product not found'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Product deleted successfully'\n        });\n    } catch (error) {\n        console.error('Products DELETE Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to delete product'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9wcm9kdWN0cy9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBd0Q7QUFDRDtBQUd2RCw2REFBNkQ7QUFDN0QsTUFBTUcsZUFBMEI7SUFDOUI7UUFDRUMsS0FBSztRQUNMQyxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLE1BQU07WUFBQztZQUFZO1lBQU07WUFBYztTQUFVO1FBQ2pEQyxVQUFVO1lBQUM7WUFBc0I7WUFBb0I7WUFBdUI7U0FBZTtRQUMzRkMsWUFBWSxJQUFJQztRQUNoQkMsWUFBWSxJQUFJRDtJQUNsQjtJQUNBO1FBQ0VSLEtBQUs7UUFDTEMsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLE9BQU87UUFDUEMsTUFBTTtRQUNOQyxNQUFNO1lBQUM7WUFBYTtZQUFXO1lBQVU7U0FBSztRQUM5Q0MsVUFBVTtZQUFDO1lBQWE7WUFBdUI7WUFBbUI7U0FBb0I7UUFDdEZDLFlBQVksSUFBSUM7UUFDaEJDLFlBQVksSUFBSUQ7SUFDbEI7SUFDQTtRQUNFUixLQUFLO1FBQ0xDLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsTUFBTTtZQUFDO1lBQVU7WUFBZ0I7WUFBWTtTQUFlO1FBQzVEQyxVQUFVO1lBQUM7WUFBc0I7WUFBZTtZQUFxQjtTQUFjO1FBQ25GQyxZQUFZLElBQUlDO1FBQ2hCQyxZQUFZLElBQUlEO0lBQ2xCO0lBQ0E7UUFDRVIsS0FBSztRQUNMQyxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLE1BQU07WUFBQztZQUFjO1lBQVk7WUFBYTtTQUFNO1FBQ3BEQyxVQUFVO1lBQUM7WUFBbUI7WUFBcUI7WUFBaUI7U0FBcUI7UUFDekZDLFlBQVksSUFBSUM7UUFDaEJDLFlBQVksSUFBSUQ7SUFDbEI7Q0FDRDtBQUVELG9DQUFvQztBQUM3QixlQUFlRSxJQUFJQyxPQUFvQjtJQUM1QyxpRUFBaUU7SUFDakUsTUFBTSxFQUFFQyxZQUFZLEVBQUUsR0FBRyxJQUFJQyxJQUFJRixRQUFRRyxHQUFHO0lBQzVDLE1BQU1DLFdBQVdILGFBQWFJLEdBQUcsQ0FBQztJQUNsQyxNQUFNQyxTQUFTTCxhQUFhSSxHQUFHLENBQUM7SUFDaEMsTUFBTUUsV0FBV04sYUFBYUksR0FBRyxDQUFDO0lBQ2xDLE1BQU1HLFdBQVdQLGFBQWFJLEdBQUcsQ0FBQztJQUNsQyxNQUFNWCxPQUFPTyxhQUFhSSxHQUFHLENBQUM7SUFDOUIsTUFBTUksU0FBU1IsYUFBYUksR0FBRyxDQUFDLGFBQWE7SUFDN0MsTUFBTUssWUFBWVQsYUFBYUksR0FBRyxDQUFDLGdCQUFnQjtJQUVuRCxJQUFJO1FBRUYsdUJBQXVCO1FBQ3ZCLElBQUlNLFFBQVF6Qix3REFBYUEsQ0FBQzBCLElBQUksQ0FBQ3pCLGlEQUFNQSxDQUFDMEIsUUFBUSxFQUFFQyxNQUFNLENBQUM7UUFFdkQsa0JBQWtCO1FBQ2xCLElBQUlWLFlBQVlBLGFBQWEsT0FBTztZQUNsQ08sUUFBUUEsTUFBTUksUUFBUSxDQUFDLFFBQVE7Z0JBQUNYO2FBQVM7UUFDM0M7UUFFQSxnQkFBZ0I7UUFDaEIsSUFBSUUsUUFBUTtZQUNWSyxRQUFRQSxNQUFNSyxFQUFFLENBQUMsQ0FBQyxZQUFZLEVBQUVWLE9BQU8scUJBQXFCLEVBQUVBLE9BQU8sQ0FBQyxDQUFDO1FBQ3pFO1FBRUEsZUFBZTtRQUNmLElBQUlDLFVBQVU7WUFDWkksUUFBUUEsTUFBTU0sR0FBRyxDQUFDLFNBQVNDLFdBQVdYO1FBQ3hDO1FBQ0EsSUFBSUMsVUFBVTtZQUNaRyxRQUFRQSxNQUFNUSxHQUFHLENBQUMsU0FBU0QsV0FBV1Y7UUFDeEM7UUFFQSxjQUFjO1FBQ2QsSUFBSWQsTUFBTTtZQUNSLE1BQU0wQixXQUFXMUIsS0FBSzJCLEtBQUssQ0FBQyxLQUFLQyxHQUFHLENBQUNDLENBQUFBLE1BQU9BLElBQUlDLElBQUk7WUFDcERiLFFBQVFBLE1BQU1jLFFBQVEsQ0FBQyxRQUFRTDtRQUNqQztRQUVBLHFCQUFxQjtRQUNyQixNQUFNTSxZQUFZaEIsY0FBYztRQUNoQ0MsUUFBUUEsTUFBTWdCLEtBQUssQ0FBQ2xCLFFBQVE7WUFBRWlCO1FBQVU7UUFFeEMsTUFBTSxFQUFFRSxNQUFNQyxRQUFRLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1uQjtRQUV4QyxJQUFJbUIsT0FBTztZQUNULE1BQU1BO1FBQ1I7UUFFQSxzREFBc0Q7UUFDdEQsTUFBTSxFQUFFRixNQUFNRyxXQUFXLEVBQUUsR0FBRyxNQUFNN0Msd0RBQWFBLENBQUMwQixJQUFJLENBQUN6QixpREFBTUEsQ0FBQzBCLFFBQVEsRUFBRUMsTUFBTSxDQUFDO1FBQy9FLE1BQU1rQixVQUFVO2VBQUksSUFBSUMsSUFBSUYsYUFBYUcsUUFBUSxDQUFDQyxJQUFXQSxFQUFFekMsSUFBSSxLQUFLLEVBQUU7U0FBRTtRQUM1RSxNQUFNMEMsYUFBYUosUUFBUUssSUFBSTtRQUUvQixnQ0FBZ0M7UUFDaEMsTUFBTSxFQUFFVCxNQUFNVSxTQUFTLEVBQUUsR0FBRyxNQUFNcEQsd0RBQWFBLENBQzVDMEIsSUFBSSxDQUFDekIsaURBQU1BLENBQUMwQixRQUFRLEVBQ3BCQyxNQUFNLENBQUMsU0FDUGEsS0FBSyxDQUFDLFNBQVM7WUFBRUQsV0FBVztRQUFLO1FBRXBDLE1BQU1hLFNBQVNELFdBQVdoQixJQUFJYSxDQUFBQSxJQUFLQSxFQUFFM0MsS0FBSyxLQUFLLEVBQUU7UUFDakQsTUFBTWdELGFBQWE7WUFBQztnQkFDbEJqQyxVQUFVZ0MsT0FBT0UsTUFBTSxHQUFHLElBQUlDLEtBQUtDLEdBQUcsSUFBSUosVUFBVTtnQkFDcEQvQixVQUFVK0IsT0FBT0UsTUFBTSxHQUFHLElBQUlDLEtBQUtFLEdBQUcsSUFBSUwsVUFBVTtZQUN0RDtTQUFFO1FBRUYsOERBQThEO1FBQzlELE1BQU1NLHNCQUFzQixDQUFDaEIsWUFBWSxFQUFFLEVBQUVQLEdBQUcsQ0FBQ3dCLENBQUFBLFVBQVk7Z0JBQzNELEdBQUdBLE9BQU87Z0JBQ1Z6RCxLQUFLeUQsUUFBUUMsRUFBRTtnQkFDZnRELE1BQU1xRCxRQUFRckQsSUFBSSxJQUFJcUQsUUFBUUMsRUFBRTtnQkFDaENyRCxNQUFNb0QsUUFBUXBELElBQUksSUFBSSxFQUFFO2dCQUN4QkMsVUFBVW1ELFFBQVFuRCxRQUFRLElBQUksRUFBRTtnQkFDaENxRCxXQUFXRixRQUFRRSxTQUFTLElBQUk7Z0JBQ2hDcEQsWUFBWSxJQUFJQyxLQUFLaUQsUUFBUWxELFVBQVU7Z0JBQ3ZDRSxZQUFZLElBQUlELEtBQUtpRCxRQUFRaEQsVUFBVSxJQUFJZ0QsUUFBUWxELFVBQVU7WUFDL0Q7UUFFQSxPQUFPWCxxREFBWUEsQ0FBQ2dFLElBQUksQ0FNcEI7WUFDRkMsU0FBUztZQUNUdEIsTUFBTTtnQkFDSkMsVUFBVWdCO2dCQUNWVDtnQkFDQUksWUFBWUEsVUFBVSxDQUFDLEVBQUUsR0FBRztvQkFDMUJHLEtBQUtILFVBQVUsQ0FBQyxFQUFFLENBQUNqQyxRQUFRO29CQUMzQnFDLEtBQUtKLFVBQVUsQ0FBQyxFQUFFLENBQUNoQyxRQUFRO2dCQUM3QixJQUFJO29CQUFFbUMsS0FBSztvQkFBR0MsS0FBSztnQkFBSztnQkFDeEJPLFlBQVlOLG9CQUFvQkosTUFBTTtnQkFDdENXLFNBQVM7b0JBQUVoRDtvQkFBVUU7b0JBQVFDO29CQUFVQztvQkFBVWQ7b0JBQU1lO29CQUFRQztnQkFBVTtZQUMzRTtZQUNBMkMsU0FBUyxDQUFDLE1BQU0sRUFBRVIsb0JBQW9CSixNQUFNLENBQUMsU0FBUyxDQUFDO1FBQ3pEO0lBRUYsRUFBRSxPQUFPWCxPQUFPO1FBQ2R3QixRQUFReEIsS0FBSyxDQUFDLHVCQUF1QkE7UUFFckMsb0NBQW9DO1FBQ3BDLElBQUlBLGlCQUFpQnlCLFNBQVN6QixNQUFNdUIsT0FBTyxDQUFDRyxRQUFRLENBQUMsUUFBUTtZQUMzREYsUUFBUUcsR0FBRyxDQUFDO1FBQ2QsT0FBTztZQUNMSCxRQUFRRyxHQUFHLENBQUM7UUFDZDtRQUVBLDZCQUE2QjtRQUM3QixJQUFJQyxtQkFBbUI7ZUFBSXRFO1NBQWE7UUFFeEMsa0JBQWtCO1FBQ2xCLElBQUlnQixZQUFZQSxhQUFhLE9BQU87WUFDbENzRCxtQkFBbUJBLGlCQUFpQkMsTUFBTSxDQUFDeEIsQ0FBQUEsSUFBS0EsRUFBRXpDLElBQUksQ0FBQzhELFFBQVEsQ0FBQ3BEO1FBQ2xFO1FBRUEsZ0JBQWdCO1FBQ2hCLElBQUlFLFFBQVE7WUFDVixNQUFNc0QsY0FBY3RELE9BQU91RCxXQUFXO1lBQ3RDSCxtQkFBbUJBLGlCQUFpQkMsTUFBTSxDQUFDeEIsQ0FBQUEsSUFDekNBLEVBQUU3QyxJQUFJLENBQUN1RSxXQUFXLEdBQUdMLFFBQVEsQ0FBQ0ksZ0JBQzlCekIsRUFBRTVDLFdBQVcsQ0FBQ3NFLFdBQVcsR0FBR0wsUUFBUSxDQUFDSSxnQkFDckN6QixFQUFFekMsSUFBSSxDQUFDb0UsSUFBSSxDQUFDdkMsQ0FBQUEsTUFBT0EsSUFBSXNDLFdBQVcsR0FBR0wsUUFBUSxDQUFDSTtRQUVsRDtRQUVBLGlEQUFpRDtRQUNqRCxPQUFPM0UscURBQVlBLENBQUNnRSxJQUFJLENBQW1GO1lBQ3pHQyxTQUFTO1lBQ1R0QixNQUFNO2dCQUNKQyxVQUFVNkI7Z0JBQ1ZLLE9BQU9MLGlCQUFpQmpCLE1BQU07Z0JBQzlCdUIsTUFBTTtnQkFDTkMsT0FBT1AsaUJBQWlCakIsTUFBTTtZQUNoQztZQUNBWSxTQUFTO1FBQ1g7SUFDRjtBQUNGO0FBRUEsNEJBQTRCO0FBQ3JCLGVBQWVhLEtBQUtsRSxPQUFvQjtJQUM3QyxJQUFJO1FBQ0YsTUFBTW1FLGNBQWMsTUFBTW5FLFFBQVFpRCxJQUFJO1FBRXRDLDJCQUEyQjtRQUMzQixNQUFNLEVBQUUzRCxJQUFJLEVBQUVDLFdBQVcsRUFBRUMsS0FBSyxFQUFFQyxJQUFJLEVBQUVDLElBQUksRUFBRSxHQUFHeUU7UUFDakQsSUFBSSxDQUFDN0UsUUFBUSxDQUFDQyxlQUFlLENBQUNDLFNBQVMsQ0FBQ0MsUUFBUSxDQUFDQyxNQUFNK0MsUUFBUTtZQUM3RCxPQUFPeEQscURBQVlBLENBQUNnRSxJQUFJLENBQWM7Z0JBQ3BDQyxTQUFTO2dCQUNUcEIsT0FBTztZQUNULEdBQUc7Z0JBQUVzQyxRQUFRO1lBQUk7UUFDbkI7UUFFQSxNQUFNQyxLQUFLLE1BQU1DO1FBRWpCLCtCQUErQjtRQUMvQixNQUFNQyxrQkFBa0IsTUFBTUYsR0FBR0csVUFBVSxDQUFDQyxZQUFZNUQsUUFBUSxFQUM3RDZELE9BQU8sQ0FBQztZQUFFakY7UUFBSztRQUVsQixJQUFJOEUsaUJBQWlCO1lBQ25CLE9BQU90RixxREFBWUEsQ0FBQ2dFLElBQUksQ0FBYztnQkFDcENDLFNBQVM7Z0JBQ1RwQixPQUFPO1lBQ1QsR0FBRztnQkFBRXNDLFFBQVE7WUFBSTtRQUNuQjtRQUVBLHFCQUFxQjtRQUNyQixNQUFNTyxhQUFtQztZQUN2Q3JGO1lBQ0FDO1lBQ0FDLE9BQU9vRixPQUFPcEY7WUFDZEM7WUFDQXVELFdBQVdtQixZQUFZbkIsU0FBUyxJQUFJO1lBQ3BDdEQsTUFBTW1GLE1BQU1DLE9BQU8sQ0FBQ3BGLFFBQVFBLE9BQU8sRUFBRTtZQUNyQ0MsVUFBVWtGLE1BQU1DLE9BQU8sQ0FBQ1gsWUFBWXhFLFFBQVEsSUFBSXdFLFlBQVl4RSxRQUFRLEdBQUcsRUFBRTtZQUN6RUMsWUFBWSxJQUFJQztZQUNoQkMsWUFBWSxJQUFJRDtRQUNsQjtRQUVBLE1BQU1rRixTQUFTLE1BQU1WLEdBQUdHLFVBQVUsQ0FBQ0MsWUFBWTVELFFBQVEsRUFBRW1FLFNBQVMsQ0FBQ0w7UUFFbkUsT0FBTzFGLHFEQUFZQSxDQUFDZ0UsSUFBSSxDQUF1QjtZQUM3Q0MsU0FBUztZQUNUdEIsTUFBTTtnQkFBRSxHQUFHK0MsVUFBVTtnQkFBRXRGLEtBQUswRixPQUFPRSxVQUFVLENBQUNDLFFBQVE7WUFBRztZQUN6RDdCLFNBQVM7UUFDWCxHQUFHO1lBQUVlLFFBQVE7UUFBSTtJQUVuQixFQUFFLE9BQU90QyxPQUFPO1FBQ2R3QixRQUFReEIsS0FBSyxDQUFDLHdCQUF3QkE7UUFDdEMsT0FBTzdDLHFEQUFZQSxDQUFDZ0UsSUFBSSxDQUFjO1lBQ3BDQyxTQUFTO1lBQ1RwQixPQUFPO1FBQ1QsR0FBRztZQUFFc0MsUUFBUTtRQUFJO0lBQ25CO0FBQ0Y7QUFFQSxnQ0FBZ0M7QUFDekIsZUFBZWUsSUFBSW5GLE9BQW9CO0lBQzVDLElBQUk7UUFDRixNQUFNbUUsY0FBYyxNQUFNbkUsUUFBUWlELElBQUk7UUFDdEMsTUFBTSxFQUFFNUQsR0FBRyxFQUFFLEdBQUcrRixZQUFZLEdBQUdqQjtRQUUvQixJQUFJLENBQUM5RSxLQUFLO1lBQ1IsT0FBT0oscURBQVlBLENBQUNnRSxJQUFJLENBQWM7Z0JBQ3BDQyxTQUFTO2dCQUNUcEIsT0FBTztZQUNULEdBQUc7Z0JBQUVzQyxRQUFRO1lBQUk7UUFDbkI7UUFFQSxNQUFNQyxLQUFLLE1BQU1DO1FBRWpCLDBCQUEwQjtRQUMxQixNQUFNQyxrQkFBa0IsTUFBTUYsR0FBR0csVUFBVSxDQUFDQyxZQUFZNUQsUUFBUSxFQUM3RDZELE9BQU8sQ0FBQztZQUFFckY7UUFBSTtRQUVqQixJQUFJLENBQUNrRixpQkFBaUI7WUFDcEIsT0FBT3RGLHFEQUFZQSxDQUFDZ0UsSUFBSSxDQUFjO2dCQUNwQ0MsU0FBUztnQkFDVHBCLE9BQU87WUFDVCxHQUFHO2dCQUFFc0MsUUFBUTtZQUFJO1FBQ25CO1FBRUEsaUJBQWlCO1FBQ2pCLE1BQU1pQixpQkFBaUI7WUFDckIsR0FBR0QsVUFBVTtZQUNiNUYsT0FBT29GLE9BQU9RLFdBQVc1RixLQUFLO1lBQzlCTSxZQUFZLElBQUlEO1FBQ2xCO1FBRUEsTUFBTXdFLEdBQUdHLFVBQVUsQ0FBQ0MsWUFBWTVELFFBQVEsRUFBRXlFLFNBQVMsQ0FDakQ7WUFBRWpHO1FBQUksR0FDTjtZQUFFa0csTUFBTUY7UUFBZTtRQUd6QixNQUFNTixTQUFTLE1BQU1WLEdBQUdHLFVBQVUsQ0FBQ0MsWUFBWTVELFFBQVEsRUFDcEQ2RCxPQUFPLENBQUM7WUFBRXJGO1FBQUk7UUFFakIsT0FBT0oscURBQVlBLENBQUNnRSxJQUFJLENBQXVCO1lBQzdDQyxTQUFTO1lBQ1R0QixNQUFNbUQ7WUFDTjFCLFNBQVM7UUFDWDtJQUVGLEVBQUUsT0FBT3ZCLE9BQU87UUFDZHdCLFFBQVF4QixLQUFLLENBQUMsdUJBQXVCQTtRQUNyQyxPQUFPN0MscURBQVlBLENBQUNnRSxJQUFJLENBQWM7WUFDcENDLFNBQVM7WUFDVHBCLE9BQU87UUFDVCxHQUFHO1lBQUVzQyxRQUFRO1FBQUk7SUFDbkI7QUFDRjtBQUVBLDBCQUEwQjtBQUNuQixlQUFlb0IsT0FBT3hGLE9BQW9CO0lBQy9DLElBQUk7UUFDRixNQUFNLEVBQUVDLFlBQVksRUFBRSxHQUFHLElBQUlDLElBQUlGLFFBQVFHLEdBQUc7UUFDNUMsTUFBTXNGLFlBQVl4RixhQUFhSSxHQUFHLENBQUM7UUFFbkMsSUFBSSxDQUFDb0YsV0FBVztZQUNkLE9BQU94RyxxREFBWUEsQ0FBQ2dFLElBQUksQ0FBYztnQkFDcENDLFNBQVM7Z0JBQ1RwQixPQUFPO1lBQ1QsR0FBRztnQkFBRXNDLFFBQVE7WUFBSTtRQUNuQjtRQUVBLE1BQU1DLEtBQUssTUFBTUM7UUFFakIsTUFBTVMsU0FBUyxNQUFNVixHQUFHRyxVQUFVLENBQUNDLFlBQVk1RCxRQUFRLEVBQ3BENkUsU0FBUyxDQUFDO1lBQUVyRyxLQUFLb0c7UUFBVTtRQUU5QixJQUFJVixPQUFPWSxZQUFZLEtBQUssR0FBRztZQUM3QixPQUFPMUcscURBQVlBLENBQUNnRSxJQUFJLENBQWM7Z0JBQ3BDQyxTQUFTO2dCQUNUcEIsT0FBTztZQUNULEdBQUc7Z0JBQUVzQyxRQUFRO1lBQUk7UUFDbkI7UUFFQSxPQUFPbkYscURBQVlBLENBQUNnRSxJQUFJLENBQWM7WUFDcENDLFNBQVM7WUFDVEcsU0FBUztRQUNYO0lBRUYsRUFBRSxPQUFPdkIsT0FBTztRQUNkd0IsUUFBUXhCLEtBQUssQ0FBQywwQkFBMEJBO1FBQ3hDLE9BQU83QyxxREFBWUEsQ0FBQ2dFLElBQUksQ0FBYztZQUNwQ0MsU0FBUztZQUNUcEIsT0FBTztRQUNULEdBQUc7WUFBRXNDLFFBQVE7UUFBSTtJQUNuQjtBQUNGIiwic291cmNlcyI6WyJHOlxcQUd0ZWNoXFxFLWNvbW1lcmNlXFxhZ3hleHBlcmllbmNlLXN0b3JlLW5ld1xcc3JjXFxhcHBcXGFwaVxccHJvZHVjdHNcXHJvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5pbXBvcnQgeyBzdXBhYmFzZUFkbWluLCBUQUJMRVMgfSBmcm9tICdAL2xpYi9zdXBhYmFzZSc7XG5pbXBvcnQgeyBQcm9kdWN0LCBBcGlSZXNwb25zZSB9IGZyb20gJ0AvdHlwZXMnO1xuXG4vLyDwn5SEIE1vY2sgZGF0YSBmb3IgZGV2ZWxvcG1lbnQgd2hlbiBNb25nb0RCIGlzIG5vdCBhdmFpbGFibGVcbmNvbnN0IG1vY2tQcm9kdWN0czogUHJvZHVjdFtdID0gW1xuICB7XG4gICAgX2lkOiAnMScsXG4gICAgbmFtZTogJ0JvdCBUZWxlZ3JhbSBBSSBQcmVtaXVtJyxcbiAgICBkZXNjcmlwdGlvbjogJ0JvdCBUZWxlZ3JhbSBwZXJzb25hbGl6emF0byBjb24gaW50ZWxsaWdlbnphIGFydGlmaWNpYWxlIGF2YW56YXRhIHBlciBhdXRvbWF0aXp6YXJlIGxlIHR1ZSBjb252ZXJzYXppb25pIGUgZ2VzdGlyZSBpbCBjdXN0b21lciBzZXJ2aWNlLicsXG4gICAgcHJpY2U6IDI5OS45OSxcbiAgICBzbHVnOiAnYm90LXRlbGVncmFtLWFpLXByZW1pdW0nLFxuICAgIHRhZ3M6IFsndGVsZWdyYW0nLCAnYWknLCAnYXV0b21hdGlvbicsICdwcmVtaXVtJ10sXG4gICAgZmVhdHVyZXM6IFsnQUkgQ29udmVyc2F6aW9uYWxlJywgJ0ludGVncmF6aW9uZSBBUEknLCAnRGFzaGJvYXJkIEFuYWx5dGljcycsICdTdXBwb3J0IDI0LzcnXSxcbiAgICBjcmVhdGVkX2F0OiBuZXcgRGF0ZSgpLFxuICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKClcbiAgfSxcbiAge1xuICAgIF9pZDogJzInLFxuICAgIG5hbWU6ICdQb3J0Zm9saW8gV2Vic2l0ZSBGdXR1cmlzdGljbycsXG4gICAgZGVzY3JpcHRpb246ICdTaXRvIHdlYiBwb3J0Zm9saW8gY29uIGRlc2lnbiBmdXR1cmlzdGljbywgYW5pbWF6aW9uaSAzRCBlIGludGVncmF6aW9uZSBBSSBwZXIgbW9zdHJhcmUgaSB0dW9pIHByb2dldHRpIGluIG1vZG8gaW5ub3ZhdGl2by4nLFxuICAgIHByaWNlOiA1OTkuOTksXG4gICAgc2x1ZzogJ3BvcnRmb2xpby13ZWJzaXRlLWZ1dHVyaXN0aWNvJyxcbiAgICB0YWdzOiBbJ3BvcnRmb2xpbycsICd3ZWJzaXRlJywgJ2Rlc2lnbicsICczZCddLFxuICAgIGZlYXR1cmVzOiBbJ0Rlc2lnbiAzRCcsICdBbmltYXppb25pIEF2YW56YXRlJywgJ1NFTyBPdHRpbWl6emF0bycsICdNb2JpbGUgUmVzcG9uc2l2ZSddLFxuICAgIGNyZWF0ZWRfYXQ6IG5ldyBEYXRlKCksXG4gICAgdXBkYXRlZF9hdDogbmV3IERhdGUoKVxuICB9LFxuICB7XG4gICAgX2lkOiAnMycsXG4gICAgbmFtZTogJ1RlbXBsYXRlIE5vdGlvbiBQcm8nLFxuICAgIGRlc2NyaXB0aW9uOiAnVGVtcGxhdGUgTm90aW9uIHByb2Zlc3Npb25hbGUgcGVyIG9yZ2FuaXp6YXJlIHByb2dldHRpLCB0YXNrIGUga25vd2xlZGdlIGJhc2UgY29uIHNpc3RlbWEgZGkgYXV0b21hemlvbmUgaW50ZWdyYXRvLicsXG4gICAgcHJpY2U6IDQ5Ljk5LFxuICAgIHNsdWc6ICd0ZW1wbGF0ZS1ub3Rpb24tcHJvJyxcbiAgICB0YWdzOiBbJ25vdGlvbicsICdwcm9kdWN0aXZpdHknLCAndGVtcGxhdGUnLCAnb3JnYW5pemF0aW9uJ10sXG4gICAgZmVhdHVyZXM6IFsnRGFzaGJvYXJkIENvbXBsZXRhJywgJ0F1dG9tYXppb25pJywgJ1RlbXBsYXRlIE11bHRpcGxpJywgJ0d1aWRhIFNldHVwJ10sXG4gICAgY3JlYXRlZF9hdDogbmV3IERhdGUoKSxcbiAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpXG4gIH0sXG4gIHtcbiAgICBfaWQ6ICc0JyxcbiAgICBuYW1lOiAnU2lzdGVtYSBBdXRvbWF6aW9uZSBCdXNpbmVzcycsXG4gICAgZGVzY3JpcHRpb246ICdTdWl0ZSBjb21wbGV0YSBkaSBhdXRvbWF6aW9uaSBwZXIgaWwgdHVvIGJ1c2luZXNzOiBlbWFpbCBtYXJrZXRpbmcsIHNvY2lhbCBtZWRpYSwgQ1JNIGUgYW5hbHl0aWNzIGludGVncmF0aS4nLFxuICAgIHByaWNlOiA4OTkuOTksXG4gICAgc2x1ZzogJ3Npc3RlbWEtYXV0b21hemlvbmUtYnVzaW5lc3MnLFxuICAgIHRhZ3M6IFsnYXV0b21hdGlvbicsICdidXNpbmVzcycsICdtYXJrZXRpbmcnLCAnY3JtJ10sXG4gICAgZmVhdHVyZXM6IFsnRW1haWwgTWFya2V0aW5nJywgJ1NvY2lhbCBBdXRvbWF0aW9uJywgJ0NSTSBJbnRlZ3JhdG8nLCAnQW5hbHl0aWNzIEF2YW56YXRpJ10sXG4gICAgY3JlYXRlZF9hdDogbmV3IERhdGUoKSxcbiAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpXG4gIH1cbl07XG5cbi8vIEdFVCAtIEZldGNoIHByb2R1Y3RzIHdpdGggZmlsdGVyc1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIEdFVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICAvLyBFeHRyYWN0IHBhcmFtZXRlcnMgb3V0c2lkZSB0cnktY2F0Y2ggZm9yIGVycm9yIGhhbmRsaW5nIGFjY2Vzc1xuICBjb25zdCB7IHNlYXJjaFBhcmFtcyB9ID0gbmV3IFVSTChyZXF1ZXN0LnVybCk7XG4gIGNvbnN0IGNhdGVnb3J5ID0gc2VhcmNoUGFyYW1zLmdldCgnY2F0ZWdvcnknKTtcbiAgY29uc3Qgc2VhcmNoID0gc2VhcmNoUGFyYW1zLmdldCgnc2VhcmNoJyk7XG4gIGNvbnN0IG1pblByaWNlID0gc2VhcmNoUGFyYW1zLmdldCgnbWluUHJpY2UnKTtcbiAgY29uc3QgbWF4UHJpY2UgPSBzZWFyY2hQYXJhbXMuZ2V0KCdtYXhQcmljZScpO1xuICBjb25zdCB0YWdzID0gc2VhcmNoUGFyYW1zLmdldCgndGFncycpO1xuICBjb25zdCBzb3J0QnkgPSBzZWFyY2hQYXJhbXMuZ2V0KCdzb3J0QnknKSB8fCAnY3JlYXRlZF9hdCc7XG4gIGNvbnN0IHNvcnRPcmRlciA9IHNlYXJjaFBhcmFtcy5nZXQoJ3NvcnRPcmRlcicpIHx8ICdkZXNjJztcblxuICB0cnkge1xuXG4gICAgLy8gQnVpbGQgU3VwYWJhc2UgcXVlcnlcbiAgICBsZXQgcXVlcnkgPSBzdXBhYmFzZUFkbWluLmZyb20oVEFCTEVTLlBST0RVQ1RTKS5zZWxlY3QoJyonKTtcblxuICAgIC8vIENhdGVnb3J5IGZpbHRlclxuICAgIGlmIChjYXRlZ29yeSAmJiBjYXRlZ29yeSAhPT0gJ2FsbCcpIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkuY29udGFpbnMoJ3RhZ3MnLCBbY2F0ZWdvcnldKTtcbiAgICB9XG5cbiAgICAvLyBTZWFyY2ggZmlsdGVyXG4gICAgaWYgKHNlYXJjaCkge1xuICAgICAgcXVlcnkgPSBxdWVyeS5vcihgbmFtZS5pbGlrZS4lJHtzZWFyY2h9JSxkZXNjcmlwdGlvbi5pbGlrZS4lJHtzZWFyY2h9JWApO1xuICAgIH1cblxuICAgIC8vIFByaWNlIGZpbHRlclxuICAgIGlmIChtaW5QcmljZSkge1xuICAgICAgcXVlcnkgPSBxdWVyeS5ndGUoJ3ByaWNlJywgcGFyc2VGbG9hdChtaW5QcmljZSkpO1xuICAgIH1cbiAgICBpZiAobWF4UHJpY2UpIHtcbiAgICAgIHF1ZXJ5ID0gcXVlcnkubHRlKCdwcmljZScsIHBhcnNlRmxvYXQobWF4UHJpY2UpKTtcbiAgICB9XG5cbiAgICAvLyBUYWdzIGZpbHRlclxuICAgIGlmICh0YWdzKSB7XG4gICAgICBjb25zdCB0YWdBcnJheSA9IHRhZ3Muc3BsaXQoJywnKS5tYXAodGFnID0+IHRhZy50cmltKCkpO1xuICAgICAgcXVlcnkgPSBxdWVyeS5vdmVybGFwcygndGFncycsIHRhZ0FycmF5KTtcbiAgICB9XG5cbiAgICAvLyBTb3J0IGNvbmZpZ3VyYXRpb25cbiAgICBjb25zdCBhc2NlbmRpbmcgPSBzb3J0T3JkZXIgPT09ICdhc2MnO1xuICAgIHF1ZXJ5ID0gcXVlcnkub3JkZXIoc29ydEJ5LCB7IGFzY2VuZGluZyB9KTtcblxuICAgIGNvbnN0IHsgZGF0YTogcHJvZHVjdHMsIGVycm9yIH0gPSBhd2FpdCBxdWVyeTtcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuXG4gICAgLy8gR2V0IGFsbCB1bmlxdWUgY2F0ZWdvcmllcyAodGFncykgZm9yIGZpbHRlciBvcHRpb25zXG4gICAgY29uc3QgeyBkYXRhOiBhbGxQcm9kdWN0cyB9ID0gYXdhaXQgc3VwYWJhc2VBZG1pbi5mcm9tKFRBQkxFUy5QUk9EVUNUUykuc2VsZWN0KCd0YWdzJyk7XG4gICAgY29uc3QgYWxsVGFncyA9IFsuLi5uZXcgU2V0KGFsbFByb2R1Y3RzPy5mbGF0TWFwKChwOiBhbnkpID0+IHAudGFncykgfHwgW10pXTtcbiAgICBjb25zdCBjYXRlZ29yaWVzID0gYWxsVGFncy5zb3J0KCk7XG5cbiAgICAvLyBHZXQgcHJpY2UgcmFuZ2UgZnJvbSBTdXBhYmFzZVxuICAgIGNvbnN0IHsgZGF0YTogcHJpY2VEYXRhIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluXG4gICAgICAuZnJvbShUQUJMRVMuUFJPRFVDVFMpXG4gICAgICAuc2VsZWN0KCdwcmljZScpXG4gICAgICAub3JkZXIoJ3ByaWNlJywgeyBhc2NlbmRpbmc6IHRydWUgfSk7XG5cbiAgICBjb25zdCBwcmljZXMgPSBwcmljZURhdGE/Lm1hcChwID0+IHAucHJpY2UpIHx8IFtdO1xuICAgIGNvbnN0IHByaWNlUmFuZ2UgPSBbe1xuICAgICAgbWluUHJpY2U6IHByaWNlcy5sZW5ndGggPiAwID8gTWF0aC5taW4oLi4ucHJpY2VzKSA6IDAsXG4gICAgICBtYXhQcmljZTogcHJpY2VzLmxlbmd0aCA+IDAgPyBNYXRoLm1heCguLi5wcmljZXMpIDogMTAwMFxuICAgIH1dO1xuXG4gICAgLy8gVHJhbnNmb3JtIFN1cGFiYXNlIHByb2R1Y3RzIHRvIGluY2x1ZGUgY29tcGF0aWJpbGl0eSBmaWVsZHNcbiAgICBjb25zdCB0cmFuc2Zvcm1lZFByb2R1Y3RzID0gKHByb2R1Y3RzIHx8IFtdKS5tYXAocHJvZHVjdCA9PiAoe1xuICAgICAgLi4ucHJvZHVjdCxcbiAgICAgIF9pZDogcHJvZHVjdC5pZCwgLy8gQWRkIE1vbmdvREItc3R5bGUgX2lkIGZvciBjb21wYXRpYmlsaXR5XG4gICAgICBzbHVnOiBwcm9kdWN0LnNsdWcgfHwgcHJvZHVjdC5pZCwgLy8gVXNlIHNsdWcgaWYgYXZhaWxhYmxlLCBvdGhlcndpc2UgdXNlIGlkXG4gICAgICB0YWdzOiBwcm9kdWN0LnRhZ3MgfHwgW10sIC8vIEVuc3VyZSB0YWdzIGlzIGFsd2F5cyBhbiBhcnJheVxuICAgICAgZmVhdHVyZXM6IHByb2R1Y3QuZmVhdHVyZXMgfHwgW10sIC8vIEVuc3VyZSBmZWF0dXJlcyBpcyBhbHdheXMgYW4gYXJyYXlcbiAgICAgIGltYWdlX3VybDogcHJvZHVjdC5pbWFnZV91cmwgfHwgJycsIC8vIEVuc3VyZSBpbWFnZV91cmwgaXMgYWx3YXlzIGEgc3RyaW5nXG4gICAgICBjcmVhdGVkX2F0OiBuZXcgRGF0ZShwcm9kdWN0LmNyZWF0ZWRfYXQpLFxuICAgICAgdXBkYXRlZF9hdDogbmV3IERhdGUocHJvZHVjdC51cGRhdGVkX2F0IHx8IHByb2R1Y3QuY3JlYXRlZF9hdClcbiAgICB9KSk7XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb248QXBpUmVzcG9uc2U8e1xuICAgICAgcHJvZHVjdHM6IFByb2R1Y3RbXTtcbiAgICAgIGNhdGVnb3JpZXM6IHN0cmluZ1tdO1xuICAgICAgcHJpY2VSYW5nZTogeyBtaW46IG51bWJlcjsgbWF4OiBudW1iZXIgfTtcbiAgICAgIHRvdGFsQ291bnQ6IG51bWJlcjtcbiAgICAgIGZpbHRlcnM6IGFueTtcbiAgICB9Pj4oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIGRhdGE6IHtcbiAgICAgICAgcHJvZHVjdHM6IHRyYW5zZm9ybWVkUHJvZHVjdHMsXG4gICAgICAgIGNhdGVnb3JpZXMsXG4gICAgICAgIHByaWNlUmFuZ2U6IHByaWNlUmFuZ2VbMF0gPyB7XG4gICAgICAgICAgbWluOiBwcmljZVJhbmdlWzBdLm1pblByaWNlLFxuICAgICAgICAgIG1heDogcHJpY2VSYW5nZVswXS5tYXhQcmljZVxuICAgICAgICB9IDogeyBtaW46IDAsIG1heDogMTAwMCB9LFxuICAgICAgICB0b3RhbENvdW50OiB0cmFuc2Zvcm1lZFByb2R1Y3RzLmxlbmd0aCxcbiAgICAgICAgZmlsdGVyczogeyBjYXRlZ29yeSwgc2VhcmNoLCBtaW5QcmljZSwgbWF4UHJpY2UsIHRhZ3MsIHNvcnRCeSwgc29ydE9yZGVyIH1cbiAgICAgIH0sXG4gICAgICBtZXNzYWdlOiBgRm91bmQgJHt0cmFuc2Zvcm1lZFByb2R1Y3RzLmxlbmd0aH0gcHJvZHVjdHNgXG4gICAgfSk7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdQcm9kdWN0cyBHRVQgRXJyb3I6JywgZXJyb3IpO1xuXG4gICAgLy8gQ2hlY2sgaWYgaXQncyBhIE1vbmdvREIgU1NMIGVycm9yXG4gICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IgJiYgZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnU1NMJykpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SnIE1vbmdvREIgU1NMIEVycm9yIGRldGVjdGVkIC0gVXNpbmcgbW9jayBkYXRhIGZvciBkZXZlbG9wbWVudC4uLicpO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+UhCBEYXRhYmFzZSBjb25uZWN0aW9uIGZhaWxlZCAtIFVzaW5nIG1vY2sgZGF0YSBmb3IgZGV2ZWxvcG1lbnQuLi4nKTtcbiAgICB9XG5cbiAgICAvLyBBcHBseSBmaWx0ZXJzIHRvIG1vY2sgZGF0YVxuICAgIGxldCBmaWx0ZXJlZFByb2R1Y3RzID0gWy4uLm1vY2tQcm9kdWN0c107XG5cbiAgICAvLyBDYXRlZ29yeSBmaWx0ZXJcbiAgICBpZiAoY2F0ZWdvcnkgJiYgY2F0ZWdvcnkgIT09ICdhbGwnKSB7XG4gICAgICBmaWx0ZXJlZFByb2R1Y3RzID0gZmlsdGVyZWRQcm9kdWN0cy5maWx0ZXIocCA9PiBwLnRhZ3MuaW5jbHVkZXMoY2F0ZWdvcnkpKTtcbiAgICB9XG5cbiAgICAvLyBTZWFyY2ggZmlsdGVyXG4gICAgaWYgKHNlYXJjaCkge1xuICAgICAgY29uc3Qgc2VhcmNoTG93ZXIgPSBzZWFyY2gudG9Mb3dlckNhc2UoKTtcbiAgICAgIGZpbHRlcmVkUHJvZHVjdHMgPSBmaWx0ZXJlZFByb2R1Y3RzLmZpbHRlcihwID0+XG4gICAgICAgIHAubmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaExvd2VyKSB8fFxuICAgICAgICBwLmRlc2NyaXB0aW9uLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoTG93ZXIpIHx8XG4gICAgICAgIHAudGFncy5zb21lKHRhZyA9PiB0YWcudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hMb3dlcikpXG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIFJldHVybiBtb2NrIGRhdGEgd2hlbiBNb25nb0RCIGlzIG5vdCBhdmFpbGFibGVcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb248QXBpUmVzcG9uc2U8eyBwcm9kdWN0czogUHJvZHVjdFtdLCB0b3RhbDogbnVtYmVyLCBwYWdlOiBudW1iZXIsIGxpbWl0OiBudW1iZXIgfT4+KHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBkYXRhOiB7XG4gICAgICAgIHByb2R1Y3RzOiBmaWx0ZXJlZFByb2R1Y3RzLFxuICAgICAgICB0b3RhbDogZmlsdGVyZWRQcm9kdWN0cy5sZW5ndGgsXG4gICAgICAgIHBhZ2U6IDEsXG4gICAgICAgIGxpbWl0OiBmaWx0ZXJlZFByb2R1Y3RzLmxlbmd0aFxuICAgICAgfSxcbiAgICAgIG1lc3NhZ2U6ICdVc2luZyBkZXZlbG9wbWVudCBtb2NrIGRhdGEgLSBNb25nb0RCIGNvbm5lY3Rpb24gZmFpbGVkJ1xuICAgIH0pO1xuICB9XG59XG5cbi8vIFBPU1QgLSBDcmVhdGUgbmV3IHByb2R1Y3RcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgcHJvZHVjdERhdGEgPSBhd2FpdCByZXF1ZXN0Lmpzb24oKTtcblxuICAgIC8vIFZhbGlkYXRlIHJlcXVpcmVkIGZpZWxkc1xuICAgIGNvbnN0IHsgbmFtZSwgZGVzY3JpcHRpb24sIHByaWNlLCBzbHVnLCB0YWdzIH0gPSBwcm9kdWN0RGF0YTtcbiAgICBpZiAoIW5hbWUgfHwgIWRlc2NyaXB0aW9uIHx8ICFwcmljZSB8fCAhc2x1ZyB8fCAhdGFncz8ubGVuZ3RoKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb248QXBpUmVzcG9uc2U+KHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIGVycm9yOiAnTWlzc2luZyByZXF1aXJlZCBmaWVsZHM6IG5hbWUsIGRlc2NyaXB0aW9uLCBwcmljZSwgc2x1ZywgdGFncydcbiAgICAgIH0sIHsgc3RhdHVzOiA0MDAgfSk7XG4gICAgfVxuXG4gICAgY29uc3QgZGIgPSBhd2FpdCBnZXREYXRhYmFzZSgpO1xuXG4gICAgLy8gQ2hlY2sgaWYgc2x1ZyBhbHJlYWR5IGV4aXN0c1xuICAgIGNvbnN0IGV4aXN0aW5nUHJvZHVjdCA9IGF3YWl0IGRiLmNvbGxlY3Rpb24oQ09MTEVDVElPTlMuUFJPRFVDVFMpXG4gICAgICAuZmluZE9uZSh7IHNsdWcgfSk7XG5cbiAgICBpZiAoZXhpc3RpbmdQcm9kdWN0KSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb248QXBpUmVzcG9uc2U+KHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIGVycm9yOiAnUHJvZHVjdCB3aXRoIHRoaXMgc2x1ZyBhbHJlYWR5IGV4aXN0cydcbiAgICAgIH0sIHsgc3RhdHVzOiA0MDkgfSk7XG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIG5ldyBwcm9kdWN0XG4gICAgY29uc3QgbmV3UHJvZHVjdDogT21pdDxQcm9kdWN0LCAnX2lkJz4gPSB7XG4gICAgICBuYW1lLFxuICAgICAgZGVzY3JpcHRpb24sXG4gICAgICBwcmljZTogTnVtYmVyKHByaWNlKSxcbiAgICAgIHNsdWcsXG4gICAgICBpbWFnZV91cmw6IHByb2R1Y3REYXRhLmltYWdlX3VybCB8fCAnJyxcbiAgICAgIHRhZ3M6IEFycmF5LmlzQXJyYXkodGFncykgPyB0YWdzIDogW10sXG4gICAgICBmZWF0dXJlczogQXJyYXkuaXNBcnJheShwcm9kdWN0RGF0YS5mZWF0dXJlcykgPyBwcm9kdWN0RGF0YS5mZWF0dXJlcyA6IFtdLFxuICAgICAgY3JlYXRlZF9hdDogbmV3IERhdGUoKSxcbiAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKClcbiAgICB9O1xuXG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgZGIuY29sbGVjdGlvbihDT0xMRUNUSU9OUy5QUk9EVUNUUykuaW5zZXJ0T25lKG5ld1Byb2R1Y3QpO1xuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uPEFwaVJlc3BvbnNlPFByb2R1Y3Q+Pih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgZGF0YTogeyAuLi5uZXdQcm9kdWN0LCBfaWQ6IHJlc3VsdC5pbnNlcnRlZElkLnRvU3RyaW5nKCkgfSxcbiAgICAgIG1lc3NhZ2U6ICdQcm9kdWN0IGNyZWF0ZWQgc3VjY2Vzc2Z1bGx5J1xuICAgIH0sIHsgc3RhdHVzOiAyMDEgfSk7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdQcm9kdWN0cyBQT1NUIEVycm9yOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb248QXBpUmVzcG9uc2U+KHtcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgZXJyb3I6ICdGYWlsZWQgdG8gY3JlYXRlIHByb2R1Y3QnXG4gICAgfSwgeyBzdGF0dXM6IDUwMCB9KTtcbiAgfVxufVxuXG4vLyBQVVQgLSBVcGRhdGUgZXhpc3RpbmcgcHJvZHVjdFxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBVVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IHByb2R1Y3REYXRhID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG4gICAgY29uc3QgeyBfaWQsIC4uLnVwZGF0ZURhdGEgfSA9IHByb2R1Y3REYXRhO1xuXG4gICAgaWYgKCFfaWQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbjxBcGlSZXNwb25zZT4oe1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgZXJyb3I6ICdQcm9kdWN0IElEIGlzIHJlcXVpcmVkJ1xuICAgICAgfSwgeyBzdGF0dXM6IDQwMCB9KTtcbiAgICB9XG5cbiAgICBjb25zdCBkYiA9IGF3YWl0IGdldERhdGFiYXNlKCk7XG5cbiAgICAvLyBDaGVjayBpZiBwcm9kdWN0IGV4aXN0c1xuICAgIGNvbnN0IGV4aXN0aW5nUHJvZHVjdCA9IGF3YWl0IGRiLmNvbGxlY3Rpb24oQ09MTEVDVElPTlMuUFJPRFVDVFMpXG4gICAgICAuZmluZE9uZSh7IF9pZCB9KTtcblxuICAgIGlmICghZXhpc3RpbmdQcm9kdWN0KSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb248QXBpUmVzcG9uc2U+KHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIGVycm9yOiAnUHJvZHVjdCBub3QgZm91bmQnXG4gICAgICB9LCB7IHN0YXR1czogNDA0IH0pO1xuICAgIH1cblxuICAgIC8vIFVwZGF0ZSBwcm9kdWN0XG4gICAgY29uc3QgdXBkYXRlZFByb2R1Y3QgPSB7XG4gICAgICAuLi51cGRhdGVEYXRhLFxuICAgICAgcHJpY2U6IE51bWJlcih1cGRhdGVEYXRhLnByaWNlKSxcbiAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKClcbiAgICB9O1xuXG4gICAgYXdhaXQgZGIuY29sbGVjdGlvbihDT0xMRUNUSU9OUy5QUk9EVUNUUykudXBkYXRlT25lKFxuICAgICAgeyBfaWQgfSxcbiAgICAgIHsgJHNldDogdXBkYXRlZFByb2R1Y3QgfVxuICAgICk7XG5cbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBkYi5jb2xsZWN0aW9uKENPTExFQ1RJT05TLlBST0RVQ1RTKVxuICAgICAgLmZpbmRPbmUoeyBfaWQgfSk7XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb248QXBpUmVzcG9uc2U8UHJvZHVjdD4+KHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBkYXRhOiByZXN1bHQsXG4gICAgICBtZXNzYWdlOiAnUHJvZHVjdCB1cGRhdGVkIHN1Y2Nlc3NmdWxseSdcbiAgICB9KTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1Byb2R1Y3RzIFBVVCBFcnJvcjonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uPEFwaVJlc3BvbnNlPih7XG4gICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgIGVycm9yOiAnRmFpbGVkIHRvIHVwZGF0ZSBwcm9kdWN0J1xuICAgIH0sIHsgc3RhdHVzOiA1MDAgfSk7XG4gIH1cbn1cblxuLy8gREVMRVRFIC0gRGVsZXRlIHByb2R1Y3RcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBERUxFVEUocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHNlYXJjaFBhcmFtcyB9ID0gbmV3IFVSTChyZXF1ZXN0LnVybCk7XG4gICAgY29uc3QgcHJvZHVjdElkID0gc2VhcmNoUGFyYW1zLmdldCgnaWQnKTtcblxuICAgIGlmICghcHJvZHVjdElkKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb248QXBpUmVzcG9uc2U+KHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIGVycm9yOiAnUHJvZHVjdCBJRCBpcyByZXF1aXJlZCdcbiAgICAgIH0sIHsgc3RhdHVzOiA0MDAgfSk7XG4gICAgfVxuXG4gICAgY29uc3QgZGIgPSBhd2FpdCBnZXREYXRhYmFzZSgpO1xuXG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgZGIuY29sbGVjdGlvbihDT0xMRUNUSU9OUy5QUk9EVUNUUylcbiAgICAgIC5kZWxldGVPbmUoeyBfaWQ6IHByb2R1Y3RJZCB9KTtcblxuICAgIGlmIChyZXN1bHQuZGVsZXRlZENvdW50ID09PSAwKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb248QXBpUmVzcG9uc2U+KHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIGVycm9yOiAnUHJvZHVjdCBub3QgZm91bmQnXG4gICAgICB9LCB7IHN0YXR1czogNDA0IH0pO1xuICAgIH1cblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbjxBcGlSZXNwb25zZT4oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIG1lc3NhZ2U6ICdQcm9kdWN0IGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5J1xuICAgIH0pO1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignUHJvZHVjdHMgREVMRVRFIEVycm9yOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb248QXBpUmVzcG9uc2U+KHtcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgZXJyb3I6ICdGYWlsZWQgdG8gZGVsZXRlIHByb2R1Y3QnXG4gICAgfSwgeyBzdGF0dXM6IDUwMCB9KTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsInN1cGFiYXNlQWRtaW4iLCJUQUJMRVMiLCJtb2NrUHJvZHVjdHMiLCJfaWQiLCJuYW1lIiwiZGVzY3JpcHRpb24iLCJwcmljZSIsInNsdWciLCJ0YWdzIiwiZmVhdHVyZXMiLCJjcmVhdGVkX2F0IiwiRGF0ZSIsInVwZGF0ZWRfYXQiLCJHRVQiLCJyZXF1ZXN0Iiwic2VhcmNoUGFyYW1zIiwiVVJMIiwidXJsIiwiY2F0ZWdvcnkiLCJnZXQiLCJzZWFyY2giLCJtaW5QcmljZSIsIm1heFByaWNlIiwic29ydEJ5Iiwic29ydE9yZGVyIiwicXVlcnkiLCJmcm9tIiwiUFJPRFVDVFMiLCJzZWxlY3QiLCJjb250YWlucyIsIm9yIiwiZ3RlIiwicGFyc2VGbG9hdCIsImx0ZSIsInRhZ0FycmF5Iiwic3BsaXQiLCJtYXAiLCJ0YWciLCJ0cmltIiwib3ZlcmxhcHMiLCJhc2NlbmRpbmciLCJvcmRlciIsImRhdGEiLCJwcm9kdWN0cyIsImVycm9yIiwiYWxsUHJvZHVjdHMiLCJhbGxUYWdzIiwiU2V0IiwiZmxhdE1hcCIsInAiLCJjYXRlZ29yaWVzIiwic29ydCIsInByaWNlRGF0YSIsInByaWNlcyIsInByaWNlUmFuZ2UiLCJsZW5ndGgiLCJNYXRoIiwibWluIiwibWF4IiwidHJhbnNmb3JtZWRQcm9kdWN0cyIsInByb2R1Y3QiLCJpZCIsImltYWdlX3VybCIsImpzb24iLCJzdWNjZXNzIiwidG90YWxDb3VudCIsImZpbHRlcnMiLCJtZXNzYWdlIiwiY29uc29sZSIsIkVycm9yIiwiaW5jbHVkZXMiLCJsb2ciLCJmaWx0ZXJlZFByb2R1Y3RzIiwiZmlsdGVyIiwic2VhcmNoTG93ZXIiLCJ0b0xvd2VyQ2FzZSIsInNvbWUiLCJ0b3RhbCIsInBhZ2UiLCJsaW1pdCIsIlBPU1QiLCJwcm9kdWN0RGF0YSIsInN0YXR1cyIsImRiIiwiZ2V0RGF0YWJhc2UiLCJleGlzdGluZ1Byb2R1Y3QiLCJjb2xsZWN0aW9uIiwiQ09MTEVDVElPTlMiLCJmaW5kT25lIiwibmV3UHJvZHVjdCIsIk51bWJlciIsIkFycmF5IiwiaXNBcnJheSIsInJlc3VsdCIsImluc2VydE9uZSIsImluc2VydGVkSWQiLCJ0b1N0cmluZyIsIlBVVCIsInVwZGF0ZURhdGEiLCJ1cGRhdGVkUHJvZHVjdCIsInVwZGF0ZU9uZSIsIiRzZXQiLCJERUxFVEUiLCJwcm9kdWN0SWQiLCJkZWxldGVPbmUiLCJkZWxldGVkQ291bnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/products/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   getSupabaseClient: () => (/* binding */ getSupabaseClient),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Supabase configuration\nconst supabaseUrl = \"https://pbpzonzzkbvkuzulmdsi.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBicHpvbnp6a2J2a3V6dWxtZHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzODM4NzEsImV4cCI6MjA2ODk1OTg3MX0.WJh_4ztuXrmV3pXjZ8_4XOgK7Nb6ApSs-zs-m1vYqPo\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Client per operazioni pubbliche (frontend)\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Client per operazioni admin (backend API)\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Database helper functions\nasync function getSupabaseClient() {\n    return supabaseAdmin;\n}\n// Table names\nconst TABLES = {\n    PRODUCTS: 'products',\n    CART: 'cart',\n    ORDERS: 'orders',\n    USERS: 'users'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fproducts%2Froute&page=%2Fapi%2Fproducts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fproducts%2Froute.ts&appDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();