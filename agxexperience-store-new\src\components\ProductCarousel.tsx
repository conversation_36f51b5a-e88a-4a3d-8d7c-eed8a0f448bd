'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Product } from '@/types';
import { useAuth } from '@/contexts/AuthContext';
import AuthModal from './AuthModal';
import CartModal from './CartModal';
import { ShoppingCart, Zap, Star, ArrowRight, X, Check, ChevronLeft, ChevronRight } from 'lucide-react';


interface ProductCarouselProps {
  products: Product[];
  onProductSelect: (product: Product) => void;
  onShopAccess?: () => void;
  mode: 'catalog' | 'suggestions' | 'single';
}

export default function ProductCarousel({
  products,
  onProductSelect,
  onShopAccess,
  mode
}: ProductCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(mode === 'catalog');
  const filteredProducts = products;

  // Stati per autenticazione e carrello
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showCartModal, setShowCartModal] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');
  const [pendingAction, setPendingAction] = useState<'cart' | 'buy' | null>(null);
  const [pendingProduct, setPendingProduct] = useState<Product | null>(null);

  // Hook per autenticazione e carrello
  const { isAuthenticated, addToCart } = useAuth();

  // Funzioni per gestire carrello e acquisto
  const handleAddToCart = (product: Product) => {
    if (!isAuthenticated) {
      setPendingAction('cart');
      setPendingProduct(product);
      setAuthMode('login');
      setShowAuthModal(true);
    } else {
      addToCart({
        id: product.id || product._id,
        name: product.name,
        price: product.price,
        image_url: product.image_url
      });
      // Mostra feedback visivo
      console.log('Prodotto aggiunto al carrello:', product.name);
    }
  };

  const handleBuyNow = async (product: Product) => {
    if (!isAuthenticated) {
      setPendingAction('buy');
      setPendingProduct(product);
      setAuthMode('login');
      setShowAuthModal(true);
    } else {
      // Procedi direttamente al checkout
      await handleDirectCheckout(product);
    }
  };

  const handleDirectCheckout = async (product: Product) => {
    try {
      const response = await fetch('/api/checkout/cart', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          items: [{
            id: product.id || product._id,
            name: product.name,
            price: product.price,
            quantity: 1,
            image_url: product.image_url
          }],
          totalAmount: product.price
        }),
      });

      const { url } = await response.json();

      if (url) {
        window.location.href = url;
      }
    } catch (error) {
      console.error('Errore durante il checkout diretto:', error);
    }
  };

  const handleAuthSuccess = async (userData: any) => {
    setShowAuthModal(false);

    if (pendingAction && pendingProduct) {
      if (pendingAction === 'cart') {
        addToCart({
          id: pendingProduct.id || pendingProduct._id,
          name: pendingProduct.name,
          price: pendingProduct.price,
          image_url: pendingProduct.image_url
        });
      } else if (pendingAction === 'buy') {
        await handleDirectCheckout(pendingProduct);
      }
    }

    // Reset pending actions
    setPendingAction(null);
    setPendingProduct(null);
  };

  // Auto-play carousel
  useEffect(() => {
    if (!isAutoPlaying || filteredProducts.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % filteredProducts.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, filteredProducts.length]);

  const getProductIcon = (identifier: string) => {
    // Handle both slug and id, with safety check
    if (!identifier) return '💎';

    const id = identifier.toLowerCase();
    if (id.includes('telegram') || id.includes('bot')) return '🤖';
    if (id.includes('portfolio') || id.includes('website')) return '🌐';
    if (id.includes('notion') || id.includes('template')) return '⚡';
    if (id.includes('ai') || id.includes('content')) return '🧠';
    if (id.includes('ecommerce') || id.includes('store')) return '🛒';
    if (id.includes('social') || id.includes('media')) return '📱';
    if (id.includes('analytics') || id.includes('dashboard')) return '📊';
    if (id.includes('seo') || id.includes('optimizer')) return '🔍';
    if (id.includes('image') || id.includes('generator')) return '🎨';
    if (id.includes('suite') || id.includes('complete')) return '🚀';
    return '💎';
  };

  const nextProduct = () => {
    setCurrentIndex((prev) => (prev + 1) % filteredProducts.length);
  };

  const prevProduct = () => {
    setCurrentIndex((prev) => (prev - 1 + filteredProducts.length) % filteredProducts.length);
  };



  if (products.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="text-6xl mb-4 opacity-50">🛍️</div>
          <p className="text-gray-400">Nessun prodotto disponibile</p>
        </div>
      </div>
    );
  }

  if (mode === 'single' && products.length === 1) {
    const product = products[0];
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="h-full flex flex-col"
      >
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-white mb-2">Prodotto Suggerito</h2>
          <div className="w-12 h-1 bg-aurora rounded-full"></div>
        </div>

        <motion.div
          whileHover={{ scale: 1.02 }}
          onClick={() => onProductSelect(product)}
          className="card-aurora cursor-pointer flex-1 flex flex-col"
        >
          {/* Product Image */}
          <div className="relative w-full h-64 bg-gradient-to-br from-purple-600/20 to-blue-600/20 rounded-xl mb-4 flex items-center justify-center overflow-hidden">
            <motion.div
              whileHover={{ scale: 1.1 }}
              className="text-8xl opacity-70"
            >
              {getProductIcon(product.id || product.slug)}
            </motion.div>
          </div>

          {/* Product Info */}
          <div className="flex-1">
            <h3 className="text-xl font-bold text-white mb-2">
              {product.name} 🔥 MODIFICATO
            </h3>

            {/* Pulsante Dettagli Elegante */}
            <motion.button
              whileHover={{ scale: 1.02, x: 2 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => {
                console.log('🔍 Dettagli clicked for:', product.name);
                onProductSelect?.(product);
              }}
              className="inline-flex items-center space-x-1 text-xs text-white/70 hover:text-white transition-colors mb-3 group"
            >
              <span className="border-b border-dotted border-white/30 group-hover:border-white/60 transition-colors">
                Visualizza dettagli
              </span>
              <svg className="w-3 h-3 transform group-hover:translate-x-0.5 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </motion.button>

            <p className="text-gray-300 text-sm leading-relaxed line-clamp-2 mb-4">
              {product.description}
            </p>

            {/* Prezzo */}
            <div className="mb-4">
              <span className="text-2xl font-bold text-white">
                €{product.price}
              </span>
            </div>

            {/* Pulsanti Carrello e Acquista */}
            <div className="flex space-x-2">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleAddToCart(product);
                }}
                className="flex-1 bg-white/10 hover:bg-white/20 text-white py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-1 border border-white/20"
              >
                <ShoppingCart className="w-4 h-4" />
                <span>Carrello</span>
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleBuyNow(product);
                }}
                className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-1"
              >
                <Zap className="w-4 h-4" />
                <span>Acquista</span>
              </motion.button>
            </div>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-2 mt-4">
            {product.tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="px-2 py-1 bg-purple-600/20 text-purple-300 text-xs rounded-full border border-purple-500/30"
              >
                {tag}
              </span>
            ))}
          </div>
        </motion.div>
      </motion.div>
    );
  }

  // Debug
  console.log('ProductCarousel - Products:', products.length, 'Filtered:', filteredProducts.length);
  console.log('🎨 ProductCarousel rendering with modifications - no price, with details button');

  // Alert visibile per debug
  React.useEffect(() => {
    console.log('🚀 COMPONENTE MODIFICATO - VERSIONE AGGIORNATA CARICATA!');
  }, []);

  // Forza currentIndex a 0 per prodotto singolo
  useEffect(() => {
    if (filteredProducts.length === 1) {
      setCurrentIndex(0);
      console.log('Single product detected, setting currentIndex to 0');
    }
  }, [filteredProducts.length]);

  return (
    <div className="h-full flex flex-col">



      {/* Carousel 3D Container - Altezza Aumentata */}
      <div className="relative perspective-1000 h-64">
        {filteredProducts.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-white/60">
              <div className="text-4xl mb-2">🌟</div>
              <div className="text-sm">Caricamento prodotti...</div>
            </div>
          </div>
        ) : (
          <div
            className="relative w-full h-full overflow-visible"
            onMouseEnter={() => setIsAutoPlaying(false)}
            onMouseLeave={() => setIsAutoPlaying(mode === 'catalog')}
          >
            {/* 3D Carousel Items */}
            <div className="absolute inset-0">
              <div className="relative w-full h-full">
              {filteredProducts.map((product, index) => {
                const offset = index - currentIndex;
                const absOffset = Math.abs(offset);

                // Logica speciale per prodotto singolo
                const isSingleProduct = filteredProducts.length === 1;
                const isCenter = offset === 0 || isSingleProduct;

                // Calcolo posizione e scala per effetto 3D
                const scale = isCenter ? 1.2 : Math.max(0.6, 1 - absOffset * 0.2);
                const opacity = isCenter ? 1 : Math.max(0.3, 1 - absOffset * 0.3);
                const rotateY = isSingleProduct ? 0 : offset * 25; // No rotazione per prodotto singolo
                const translateX = isSingleProduct ? 0 : offset * 180; // Centrato per prodotto singolo
                const translateZ = isCenter ? 0 : -100 * absOffset; // Profondità

                console.log(`Product ${index}: offset=${offset}, isCenter=${isCenter}, isSingle=${isSingleProduct}`);

                return (
                  <motion.div
                    key={product.id || product._id || product.slug}
                    animate={{
                      scale,
                      opacity,
                      rotateY,
                      x: translateX,
                      z: translateZ,
                    }}
                    transition={{
                      duration: 0.6,
                      ease: [0.25, 0.46, 0.45, 0.94] // Apple-like easing
                    }}
                    className="absolute w-40 h-48 cursor-pointer"
                    style={{
                      transformStyle: 'preserve-3d',
                      filter: isCenter ? 'brightness(1.1) drop-shadow(0 20px 40px rgba(142, 45, 226, 0.3))' : 'brightness(0.8)',
                      left: '50%',
                      top: '50%',
                      marginLeft: '-80px', // metà della larghezza (w-40 = 160px)
                      marginTop: '-96px',  // metà dell'altezza (h-48 = 192px)
                    }}
                    onClick={() => {
                      if (isCenter) {
                        onProductSelect(product);
                      } else {
                        setCurrentIndex(index);
                      }
                    }}
                  >
                    {/* Card 3D Content */}
                    <div className="card-aurora h-full flex flex-col consciousness-awakening">
                      {/* Product Image 3D */}
                      <div
                        className="relative w-full h-28 rounded-md mb-2 flex items-center justify-center overflow-hidden"
                        style={{
                          background: `linear-gradient(135deg,
                            rgba(142, 45, 226, 0.2),
                            rgba(0, 216, 182, 0.2)
                          )`
                        }}
                      >
                        <motion.div
                          whileHover={isCenter ? { scale: 1.1, rotate: 5 } : {}}
                          transition={{ duration: 0.3 }}
                          className="text-3xl opacity-80"
                        >
                          {getProductIcon(product.id || product.slug)}
                        </motion.div>



                        {/* Neural Wave Effect */}
                        <div
                          className="absolute inset-0 neural-wave opacity-10 rounded-xl"
                          style={{
                            background: `linear-gradient(45deg,
                              rgba(142, 45, 226, 0.1),
                              rgba(0, 216, 182, 0.1)
                            )`
                          }}
                        ></div>
                      </div>

                      {/* Product Info Compatto */}
                      <div className="flex-1 text-center px-1">
                        <h3
                          className="text-sm font-semibold mb-2 line-clamp-2"
                          style={{ color: 'var(--aurora-champagne)' }}
                        >
                          {product.name}
                        </h3>

                        {/* Pulsante Dettagli Elegante */}
                        <motion.button
                          whileHover={{ scale: 1.02, y: -1 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => {
                            console.log('🔍 Dettagli clicked for:', product.name);
                            onProductSelect?.(product);
                          }}
                          className="inline-flex items-center space-x-1 text-xs text-white/60 hover:text-white/90 transition-all duration-200 group"
                        >
                          <span className="border-b border-dotted border-white/20 group-hover:border-white/50 transition-colors">
                            Dettagli
                          </span>
                          <svg className="w-3 h-3 transform group-hover:translate-x-0.5 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </motion.button>
                      </div>


                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Navigation Arrows 3D */}
          {filteredProducts.length > 1 && (
            <>
              <motion.button
                whileHover={{ scale: 1.1, x: -8 }}
                whileTap={{ scale: 0.9 }}
                onClick={prevProduct}
                className="absolute left-2 top-1/2 transform -translate-y-1/2 w-8 h-8 glass border border-white/20 rounded-full flex items-center justify-center text-white transition-all z-20 shadow-lg"
                style={{
                  background: 'linear-gradient(135deg, var(--aurora-purple), var(--aurora-teal))',
                  color: 'var(--aurora-champagne)'
                }}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.1, x: 8 }}
                whileTap={{ scale: 0.9 }}
                onClick={nextProduct}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 glass border border-white/20 rounded-full flex items-center justify-center text-white transition-all z-20 shadow-lg"
                style={{
                  background: 'linear-gradient(135deg, var(--aurora-purple), var(--aurora-teal))',
                  color: 'var(--aurora-champagne)'
                }}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </motion.button>
            </>
          )}
          </div>
        )}

        {/* Dots Indicator con palette corretta */}
        {filteredProducts.length > 1 && (
          <div className="flex justify-center mt-4 space-x-2">
            {filteredProducts.map((_, index) => (
              <motion.button
                key={index}
                whileHover={{ scale: 1.2 }}
                whileTap={{ scale: 0.8 }}
                onClick={() => setCurrentIndex(index)}
                className="w-2 h-2 rounded-full transition-all duration-300"
                style={{
                  backgroundColor: index === currentIndex ? 'var(--aurora-teal)' : 'var(--aurora-charcoal)',
                  boxShadow: index === currentIndex ? '0 0 10px rgba(0, 216, 182, 0.5)' : 'none'
                }}
              />
            ))}
          </div>
        )}

        {/* Messaggio per prodotto singolo */}
        {filteredProducts.length === 1 && (
          <div className="flex justify-center mt-4">
            <div className="px-4 py-2 glass border border-white/20 rounded-full">
              <span className="text-white/70 text-sm">Prodotto unico per questa categoria</span>
            </div>
          </div>
        )}

        {/* Pulsante Liquid Glass Nero Premium */}
        <div className="flex justify-center mt-6">
          <motion.button
            whileHover={{
              scale: 1.05,
              boxShadow: '0 15px 35px rgba(0, 0, 0, 0.8), 0 0 30px rgba(212, 175, 55, 0.3)'
            }}
            whileTap={{ scale: 0.95 }}
            onClick={onShopAccess}
            className="group relative px-5 py-2 rounded-lg font-bold text-xs text-white overflow-hidden transition-all duration-500 border border-white/10"
            style={{
              background: `
                linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(20, 20, 20, 0.95) 50%, rgba(0, 0, 0, 0.9) 100%),
                radial-gradient(circle at 50% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 70%)
              `,
              backdropFilter: 'blur(20px)',
              boxShadow: `
                0 8px 32px rgba(0, 0, 0, 0.6),
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                inset 0 -1px 0 rgba(0, 0, 0, 0.5)
              `
            }}
          >
            {/* Liquid Glass Effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />

            {/* Premium Glow */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-yellow-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

            {/* Contenuto Premium */}
            <div className="relative flex items-center space-x-1.5">
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
              <span className="bg-gradient-to-r from-white via-yellow-200 to-white bg-clip-text text-transparent">
                PREMIUM SHOP
              </span>
              <motion.svg
                className="w-2.5 h-2.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                whileHover={{ x: 1 }}
                transition={{ duration: 0.2 }}
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </motion.svg>
            </div>
          </motion.button>
        </div>
      </div>

      {/* Modali per autenticazione e carrello */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        mode={authMode}
        onSuccess={handleAuthSuccess}
        redirectToCheckout={pendingAction === 'buy'}
      />

      <CartModal
        isOpen={showCartModal}
        onClose={() => setShowCartModal(false)}
      />
    </div>
  );
}
