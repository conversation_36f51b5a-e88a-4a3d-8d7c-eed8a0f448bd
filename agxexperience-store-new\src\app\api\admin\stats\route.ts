import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

// Helper functions
function calculateGrowthRate(data: any[], dateField: string): number {
  if (!data || data.length === 0) return 0;

  const now = new Date();
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  const lastMonthCount = data.filter(item => {
    const itemDate = new Date(item[dateField]);
    return itemDate >= lastMonth && itemDate < thisMonth;
  }).length;

  const thisMonthCount = data.filter(item => {
    const itemDate = new Date(item[dateField]);
    return itemDate >= thisMonth;
  }).length;

  if (lastMonthCount === 0) return thisMonthCount > 0 ? 100 : 0;
  return ((thisMonthCount - lastMonthCount) / lastMonthCount) * 100;
}

function calculateRevenueGrowthRate(orders: any[]): number {
  if (!orders || orders.length === 0) return 0;

  const now = new Date();
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

  const lastMonthRevenue = orders
    .filter(order => {
      const orderDate = new Date(order.created_at);
      return orderDate >= lastMonth && orderDate < thisMonth;
    })
    .reduce((sum, order) => sum + parseFloat(order.total_amount), 0);

  const thisMonthRevenue = orders
    .filter(order => {
      const orderDate = new Date(order.created_at);
      return orderDate >= thisMonth;
    })
    .reduce((sum, order) => sum + parseFloat(order.total_amount), 0);

  if (lastMonthRevenue === 0) return thisMonthRevenue > 0 ? 100 : 0;
  return ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100;
}

export async function GET(request: NextRequest) {
  try {
    // Fetch products
    const { data: products, error: productsError } = await supabaseAdmin
      .from('products')
      .select('*');

    if (productsError) throw productsError;

    // Fetch real orders from Supabase
    const { data: orders, error: ordersError } = await supabaseAdmin
      .from('orders')
      .select(`
        *,
        order_items (
          id,
          product_id,
          product_name,
          product_price,
          quantity,
          total_price
        )
      `)
      .order('created_at', { ascending: false });

    if (ordersError) {
      console.error('Orders fetch error:', ordersError);
    }

    // Fetch users
    const { data: users, error: usersError } = await supabaseAdmin
      .from('user_profiles')
      .select('*');

    if (usersError) {
      console.error('Users fetch error:', usersError);
    }

    // Fetch analytics events for additional insights
    const { data: analyticsEvents, error: analyticsError } = await supabaseAdmin
      .from('analytics_events')
      .select('*')
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
      .order('created_at', { ascending: false });

    if (analyticsError) {
      console.error('Analytics fetch error:', analyticsError);
    }

    // Use real orders or fallback to empty array
    const ordersData = orders || [];
    const usersData = users || [];
    const analyticsData = analyticsEvents || [];

    // Calculate real stats
    const totalRevenue = ordersData.reduce((sum, order) => sum + parseFloat(order.total_amount), 0);

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayOrders = ordersData.filter(order =>
      new Date(order.created_at) >= today
    );
    const todayRevenue = todayOrders.reduce((sum, order) => sum + parseFloat(order.total_amount), 0);

    const thisMonth = new Date();
    thisMonth.setDate(1);
    thisMonth.setHours(0, 0, 0, 0);
    const monthlyOrders = ordersData.filter(order =>
      new Date(order.created_at) >= thisMonth
    );
    const monthlyRevenue = monthlyOrders.reduce((sum, order) => sum + parseFloat(order.total_amount), 0);

    const completedOrders = ordersData.filter(order => order.status === 'completed');
    const conversionRate = usersData.length > 0 ? (completedOrders.length / usersData.length) * 100 : 0;
    const avgOrderValue = ordersData.length > 0 ? totalRevenue / ordersData.length : 0;

    // Calculate page views and unique visitors from analytics
    const pageViews = analyticsData.filter(event => event.event_type === 'page_view').length;
    const uniqueVisitors = new Set(analyticsData.map(event => event.session_id)).size;

    // Calculate real top products from order items
    const productSales = {};
    ordersData.forEach(order => {
      if (order.order_items) {
        order.order_items.forEach(item => {
          if (!productSales[item.product_id]) {
            productSales[item.product_id] = {
              id: item.product_id,
              name: item.product_name,
              sales: 0,
              revenue: 0
            };
          }
          productSales[item.product_id].sales += item.quantity;
          productSales[item.product_id].revenue += parseFloat(item.total_price);
        });
      }
    });

    const topProducts = Object.values(productSales)
      .sort((a: any, b: any) => b.revenue - a.revenue)
      .slice(0, 5);

    // Generate recent orders from real data
    const recentOrders = ordersData.slice(0, 5).map(order => ({
      id: order.id,
      customer: order.customer_email || order.customer_name || 'Cliente Anonimo',
      amount: parseFloat(order.total_amount),
      status: order.status,
      date: order.created_at
    }));

    // Generate real user growth data (last 6 months)
    const userGrowth = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
      const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

      const monthUsers = usersData.filter(user => {
        const userDate = new Date(user.created_at || user.last_seen);
        return userDate >= monthStart && userDate <= monthEnd;
      }).length;

      userGrowth.push({
        month: date.toLocaleDateString('it-IT', { month: 'short' }),
        users: monthUsers
      });
    }

    // Generate real sales chart data (last 7 days)
    const salesChart = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      date.setHours(0, 0, 0, 0);

      const nextDay = new Date(date);
      nextDay.setDate(nextDay.getDate() + 1);

      const dayOrders = ordersData.filter(order => {
        const orderDate = new Date(order.created_at);
        return orderDate >= date && orderDate < nextDay;
      });

      const dayRevenue = dayOrders.reduce((sum, order) => sum + parseFloat(order.total_amount), 0);

      salesChart.push({
        date: date.toISOString().split('T')[0],
        sales: dayOrders.length,
        revenue: dayRevenue
      });
    }

    const stats = {
      totalProducts: products?.length || 0,
      totalOrders: ordersData.length,
      totalUsers: usersData.length,
      revenue: totalRevenue,
      todayRevenue,
      monthlyRevenue,
      conversionRate,
      avgOrderValue,
      topProducts,
      recentOrders,
      userGrowth,
      salesChart,
      // Additional real metrics
      pageViews,
      uniqueVisitors,
      completedOrdersCount: completedOrders.length,
      pendingOrdersCount: ordersData.filter(o => o.status === 'pending').length,
      processingOrdersCount: ordersData.filter(o => o.status === 'processing').length,
      cancelledOrdersCount: ordersData.filter(o => o.status === 'cancelled').length,
      // Growth rates
      orderGrowthRate: calculateGrowthRate(ordersData, 'created_at'),
      userGrowthRate: calculateGrowthRate(usersData, 'created_at'),
      revenueGrowthRate: calculateRevenueGrowthRate(ordersData)
    };

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Error fetching admin stats:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch admin stats' },
      { status: 500 }
    );
  }
}
