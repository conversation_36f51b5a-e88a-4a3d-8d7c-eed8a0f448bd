/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/stats/route";
exports.ids = ["app/api/admin/stats/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var G_AGtech_E_commerce_agxexperience_store_new_src_app_api_admin_stats_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/admin/stats/route.ts */ \"(rsc)/./src/app/api/admin/stats/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/stats/route\",\n        pathname: \"/api/admin/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/stats/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\app\\\\api\\\\admin\\\\stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: G_AGtech_E_commerce_agxexperience_store_new_src_app_api_admin_stats_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/admin/stats/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/stats/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/admin/stats/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\n// Helper functions\nfunction calculateGrowthRate(data, dateField) {\n    if (!data || data.length === 0) return 0;\n    const now = new Date();\n    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);\n    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);\n    const lastMonthCount = data.filter((item)=>{\n        const itemDate = new Date(item[dateField]);\n        return itemDate >= lastMonth && itemDate < thisMonth;\n    }).length;\n    const thisMonthCount = data.filter((item)=>{\n        const itemDate = new Date(item[dateField]);\n        return itemDate >= thisMonth;\n    }).length;\n    if (lastMonthCount === 0) return thisMonthCount > 0 ? 100 : 0;\n    return (thisMonthCount - lastMonthCount) / lastMonthCount * 100;\n}\nfunction calculateRevenueGrowthRate(orders) {\n    if (!orders || orders.length === 0) return 0;\n    const now = new Date();\n    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);\n    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);\n    const lastMonthRevenue = orders.filter((order)=>{\n        const orderDate = new Date(order.created_at);\n        return orderDate >= lastMonth && orderDate < thisMonth;\n    }).reduce((sum, order)=>sum + parseFloat(order.total_amount), 0);\n    const thisMonthRevenue = orders.filter((order)=>{\n        const orderDate = new Date(order.created_at);\n        return orderDate >= thisMonth;\n    }).reduce((sum, order)=>sum + parseFloat(order.total_amount), 0);\n    if (lastMonthRevenue === 0) return thisMonthRevenue > 0 ? 100 : 0;\n    return (thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue * 100;\n}\nasync function GET(request) {\n    try {\n        // Fetch products\n        const { data: products, error: productsError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from('products').select('*');\n        if (productsError) throw productsError;\n        // Fetch real orders from Supabase\n        const { data: orders, error: ordersError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from('orders').select(`\n        *,\n        order_items (\n          id,\n          product_id,\n          product_name,\n          product_price,\n          quantity,\n          total_price\n        )\n      `).order('created_at', {\n            ascending: false\n        });\n        if (ordersError) {\n            console.error('Orders fetch error:', ordersError);\n        }\n        // Fetch users\n        const { data: users, error: usersError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from('user_profiles').select('*');\n        if (usersError) {\n            console.error('Users fetch error:', usersError);\n        }\n        // Fetch analytics events for additional insights\n        const { data: analyticsEvents, error: analyticsError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from('analytics_events').select('*').gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()).order('created_at', {\n            ascending: false\n        });\n        if (analyticsError) {\n            console.error('Analytics fetch error:', analyticsError);\n        }\n        // Use real orders or fallback to empty array\n        const ordersData = orders || [];\n        const usersData = users || [];\n        const analyticsData = analyticsEvents || [];\n        // Calculate real stats\n        const totalRevenue = ordersData.reduce((sum, order)=>sum + parseFloat(order.total_amount), 0);\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const todayOrders = ordersData.filter((order)=>new Date(order.created_at) >= today);\n        const todayRevenue = todayOrders.reduce((sum, order)=>sum + parseFloat(order.total_amount), 0);\n        const thisMonth = new Date();\n        thisMonth.setDate(1);\n        thisMonth.setHours(0, 0, 0, 0);\n        const monthlyOrders = ordersData.filter((order)=>new Date(order.created_at) >= thisMonth);\n        const monthlyRevenue = monthlyOrders.reduce((sum, order)=>sum + parseFloat(order.total_amount), 0);\n        const completedOrders = ordersData.filter((order)=>order.status === 'completed');\n        const conversionRate = usersData.length > 0 ? completedOrders.length / usersData.length * 100 : 0;\n        const avgOrderValue = ordersData.length > 0 ? totalRevenue / ordersData.length : 0;\n        // Calculate page views and unique visitors from analytics\n        const pageViews = analyticsData.filter((event)=>event.event_type === 'page_view').length;\n        const uniqueVisitors = new Set(analyticsData.map((event)=>event.session_id)).size;\n        // Calculate real top products from order items\n        const productSales = {};\n        ordersData.forEach((order)=>{\n            if (order.order_items) {\n                order.order_items.forEach((item)=>{\n                    if (!productSales[item.product_id]) {\n                        productSales[item.product_id] = {\n                            id: item.product_id,\n                            name: item.product_name,\n                            sales: 0,\n                            revenue: 0\n                        };\n                    }\n                    productSales[item.product_id].sales += item.quantity;\n                    productSales[item.product_id].revenue += parseFloat(item.total_price);\n                });\n            }\n        });\n        const topProducts = Object.values(productSales).sort((a, b)=>b.revenue - a.revenue).slice(0, 5);\n        // Generate recent orders from real data\n        const recentOrders = ordersData.slice(0, 5).map((order)=>({\n                id: order.id,\n                customer: order.customer_email || order.customer_name || 'Cliente Anonimo',\n                amount: parseFloat(order.total_amount),\n                status: order.status,\n                date: order.created_at\n            }));\n        // Generate real user growth data (last 6 months)\n        const userGrowth = [];\n        for(let i = 5; i >= 0; i--){\n            const date = new Date();\n            date.setMonth(date.getMonth() - i);\n            const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);\n            const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);\n            const monthUsers = usersData.filter((user)=>{\n                const userDate = new Date(user.created_at || user.last_seen);\n                return userDate >= monthStart && userDate <= monthEnd;\n            }).length;\n            userGrowth.push({\n                month: date.toLocaleDateString('it-IT', {\n                    month: 'short'\n                }),\n                users: monthUsers\n            });\n        }\n        // Generate real sales chart data (last 7 days)\n        const salesChart = [];\n        for(let i = 6; i >= 0; i--){\n            const date = new Date();\n            date.setDate(date.getDate() - i);\n            date.setHours(0, 0, 0, 0);\n            const nextDay = new Date(date);\n            nextDay.setDate(nextDay.getDate() + 1);\n            const dayOrders = ordersData.filter((order)=>{\n                const orderDate = new Date(order.created_at);\n                return orderDate >= date && orderDate < nextDay;\n            });\n            const dayRevenue = dayOrders.reduce((sum, order)=>sum + parseFloat(order.total_amount), 0);\n            salesChart.push({\n                date: date.toISOString().split('T')[0],\n                sales: dayOrders.length,\n                revenue: dayRevenue\n            });\n        }\n        const stats = {\n            totalProducts: products?.length || 0,\n            totalOrders: ordersData.length,\n            totalUsers: usersData.length,\n            revenue: totalRevenue,\n            todayRevenue,\n            monthlyRevenue,\n            conversionRate,\n            avgOrderValue,\n            topProducts,\n            recentOrders,\n            userGrowth,\n            salesChart,\n            // Additional real metrics\n            pageViews,\n            uniqueVisitors,\n            completedOrdersCount: completedOrders.length,\n            pendingOrdersCount: ordersData.filter((o)=>o.status === 'pending').length,\n            processingOrdersCount: ordersData.filter((o)=>o.status === 'processing').length,\n            cancelledOrdersCount: ordersData.filter((o)=>o.status === 'cancelled').length,\n            // Growth rates\n            orderGrowthRate: calculateGrowthRate(ordersData, 'created_at'),\n            userGrowthRate: calculateGrowthRate(usersData, 'created_at'),\n            revenueGrowthRate: calculateRevenueGrowthRate(ordersData)\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: stats\n        });\n    } catch (error) {\n        console.error('Error fetching admin stats:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch admin stats'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   getSupabaseClient: () => (/* binding */ getSupabaseClient),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Supabase configuration\nconst supabaseUrl = \"https://pbpzonzzkbvkuzulmdsi.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBicHpvbnp6a2J2a3V6dWxtZHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzODM4NzEsImV4cCI6MjA2ODk1OTg3MX0.WJh_4ztuXrmV3pXjZ8_4XOgK7Nb6ApSs-zs-m1vYqPo\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Client per operazioni pubbliche (frontend)\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Client per operazioni admin (backend API)\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Database helper functions\nasync function getSupabaseClient() {\n    return supabaseAdmin;\n}\n// Table names\nconst TABLES = {\n    PRODUCTS: 'products',\n    CART: 'cart',\n    ORDERS: 'orders',\n    USERS: 'users'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fstats%2Froute&page=%2Fapi%2Fadmin%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fstats%2Froute.ts&appDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();