'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import AuroraChat from '@/components/AuroraChat';
import ProductCarousel from '@/components/ProductCarousel';
import ProductDetailModal from '@/components/ProductDetailModal';
import CartSidebar from '@/components/CartSidebar';
import UserProfileModal from '@/components/UserProfile';
import HeaderCategoryFilter from '@/components/HeaderCategoryFilter';

import { EmotionState, Product, Cart } from '@/types';

export default function HomePage() {
  // States
  const [isLoading, setIsLoading] = useState(true);
  const [products, setProducts] = useState<Product[]>([]);
  const [suggestedProducts, setSuggestedProducts] = useState<Product[]>([]);
  const [emotionState, setEmotionState] = useState<EmotionState>('curiosity');
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isProductModalOpen, setIsProductModalOpen] = useState(false);
  const [carouselMode, setCarouselMode] = useState<'catalog' | 'suggestions' | 'single'>('catalog');
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);

  const [cart, setCart] = useState<Cart | null>(null);
  const [sessionId] = useState(() => `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const [showWelcome, setShowWelcome] = useState(true);
  const [activeCategory, setActiveCategory] = useState('all');
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);

  // Initialize app
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Load products
        const response = await fetch('/api/products');
        const data = await response.json();
        if (data.success) {
          const loadedProducts = data.data.products || data.data;
          setProducts(loadedProducts);
          setFilteredProducts(loadedProducts);
        }
      } catch (error) {
        console.error('Failed to load products:', error);
      }

      // Load cart
      fetchCart();

      // Auto-open chat for welcome
      setIsChatOpen(true);
      
      // Auto-close after 5 seconds
      setTimeout(() => {
        setIsChatOpen(false);
        setShowWelcome(false);
      }, 5000);

      // Loading animation
      setTimeout(() => {
        setIsLoading(false);
      }, 2000);
    };

    initializeApp();
  }, []);

  // Filtra prodotti per categoria
  useEffect(() => {
    console.log('Filtering products:', { activeCategory, totalProducts: products.length });

    if (activeCategory === 'all') {
      setFilteredProducts(products);
      console.log('Showing all products:', products.length);
    } else {
      const filtered = products.filter(product => {
        if (!product.tags || product.tags.length === 0) {
          // Se il prodotto non ha tags, mostralo solo per 'all'
          return false;
        }

        // Cerca corrispondenza esatta o parziale
        const hasExactTag = product.tags.includes(activeCategory);
        const hasPartialTag = product.tags.some(tag =>
          tag.toLowerCase().includes(activeCategory.toLowerCase()) ||
          activeCategory.toLowerCase().includes(tag.toLowerCase())
        );

        const hasTag = hasExactTag || hasPartialTag;
        console.log(`Product ${product.name} has tag ${activeCategory}:`, hasTag, 'Tags:', product.tags);
        return hasTag;
      });
      setFilteredProducts(filtered);
      console.log('Filtered products:', filtered.length, 'for category:', activeCategory);
    }
  }, [activeCategory, products]);

  // Functions
  const handleProductSelect = (product: Product) => {
    setSelectedProduct(product);
    setIsProductModalOpen(true);
  };

  const handleProductSuggestion = (products: Product[]) => {
    setSuggestedProducts(products);
    setCarouselMode('suggestions');
  };

  const handlePurchase = async (productId: string) => {
    try {
      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ product_id: productId }),
      });
      const data = await response.json();
      if (data.success && data.data.checkout_url) {
        window.location.href = data.data.checkout_url;
      }
    } catch (error) {
      console.error('Checkout failed:', error);
    }
  };

  const addToCart = async (productId: string) => {
    try {
      const response = await fetch('/api/cart', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          session_id: sessionId,
          product_id: productId,
          quantity: 1
        }),
      });
      const data = await response.json();
      if (data.success) {
        fetchCart();
        alert('Prodotto aggiunto al carrello!');
      }
    } catch (error) {
      console.error('Add to cart failed:', error);
    }
  };

  const fetchCart = async () => {
    try {
      const response = await fetch(`/api/cart?session_id=${sessionId}`);
      const data = await response.json();
      if (data.success) {
        setCart(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch cart:', error);
    }
  };

  const openChat = () => {
    setIsChatOpen(true);
    setShowWelcome(false);
  };

  const closeChat = () => {
    setIsChatOpen(false);
  };

  const openShop = () => {
    window.location.href = '/shop';
  };

  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen liquid-grid-bg flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen liquid-grid-bg relative overflow-hidden">
      {/* Header - Logo + Filtro + Linea Arancione */}
      <div className="absolute top-4 left-4 z-30">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, type: "spring", stiffness: 200 }}
          className="flex items-center space-x-4 mb-3"
        >
          {/* Logo */}
          <div
            className="w-10 h-10 rounded-full flex items-center justify-center consciousness-awakening"
            style={{ background: 'linear-gradient(135deg, var(--ai-aura), var(--cta))' }}
          >
            <span className="text-white font-bold text-lg">A</span>
          </div>

          {/* Filtro Categorie */}
          {products.length > 0 && (
            <HeaderCategoryFilter
              categories={['all', ...Array.from(new Set(products.flatMap(p => p.tags)))]}
              activeCategory={activeCategory}
              onCategoryChange={handleCategoryChange}
            />
          )}
        </motion.div>

        {/* Linea Arancione Decorativa */}
        <motion.div
          initial={{ scaleX: 0 }}
          animate={{ scaleX: 1 }}
          transition={{ delay: 0.8, duration: 0.8 }}
          className="h-px w-full max-w-xs"
          style={{
            background: 'linear-gradient(90deg, transparent, var(--accenti), transparent)',
            transformOrigin: 'left'
          }}
        />
      </div>

      {/* Pulsante Verticale AURORA - Bordo Destro */}
      {!isChatOpen && !showWelcome && (
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 1, type: "spring", stiffness: 200 }}
          className="fixed top-0 right-0 h-screen z-30"
        >
          {/* Pulsante Verticale con Aura */}
          <div className="relative h-full">
            {/* Aura Pulsante */}
            <motion.div
              animate={{
                opacity: [0.3, 0.6, 0.3],
                boxShadow: [
                  '0 0 20px rgba(142, 45, 226, 0.3)',
                  '0 0 40px rgba(0, 216, 182, 0.4)',
                  '0 0 20px rgba(142, 45, 226, 0.3)'
                ]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="absolute inset-0 rounded-l-full"
              style={{
                background: 'linear-gradient(180deg, var(--ai-aura) 0%, var(--cta) 50%, var(--ai-aura) 100%)',
                filter: 'blur(2px)'
              }}
            />

            {/* Pulsante Principale */}
            <motion.button
              initial={{ width: '4px' }} // 1mm ≈ 4px
              whileHover={{
                width: '20px', // 5mm ≈ 20px
                transition: { duration: 0.3, ease: "easeOut" }
              }}
              whileTap={{ scale: 0.98 }}
              onClick={openChat}
              className="relative h-full rounded-l-2xl flex items-center justify-center overflow-hidden group consciousness-awakening"
              style={{
                background: `linear-gradient(180deg, var(--ai-aura) 0%, var(--cta) 50%, var(--ai-aura) 100%)`,
                boxShadow: '0 0 30px rgba(142, 45, 226, 0.4)'
              }}
            >
              {/* Testo Verticale */}
              <motion.div
                initial={{ opacity: 0 }}
                whileHover={{
                  opacity: 1,
                  transition: { delay: 0.1, duration: 0.2 }
                }}
                className="absolute inset-0 flex items-center justify-center"
                style={{ writingMode: 'vertical-rl', textOrientation: 'mixed' }}
              >
                <span className="text-white font-bold text-sm tracking-wider whitespace-nowrap transform rotate-180">
                  CHATTA CON AURORA
                </span>
              </motion.div>

              {/* Indicatore sempre visibile */}
              <motion.div
                animate={{
                  y: [-10, 10, -10],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 group-hover:opacity-0"
              >
                <div className="w-1 h-8 bg-white/80 rounded-full" />
              </motion.div>
            </motion.button>
          </div>
        </motion.div>
      )}

      {/* Main Layout */}
      <div className="relative z-10 min-h-screen pt-20 pb-8">
        <div className="flex">
          {/* Carousel - Si sposta a sinistra quando chat aperta */}
          <motion.div
            animate={{
              width: isChatOpen ? '65%' : '100%',
              marginRight: isChatOpen ? '35%' : '0%'
            }}
            transition={{ duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }}
            className="px-6"
          >
            {filteredProducts.length > 0 && (
              <ProductCarousel
                products={carouselMode === 'catalog' ? filteredProducts : suggestedProducts}
                onProductSelect={handleProductSelect}
                onShopAccess={openShop}
                mode={carouselMode}
              />
            )}
          </motion.div>

          {/* Chat - Slide da destra */}
          <AnimatePresence>
            {isChatOpen && (
              <motion.div
                key="chat-panel"
                  initial={{ x: '100%', opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  exit={{ x: '100%', opacity: 0 }}
                  transition={{
                    duration: 0.8,
                    ease: [0.25, 0.46, 0.45, 0.94]
                  }}
                  className="fixed top-0 right-0 w-[35%] h-screen glass-strong border-l border-white/20 shadow-2xl z-40 rounded-l-3xl overflow-hidden"
                >
                  <AuroraChat
                    isOpen={isChatOpen}
                    onClose={closeChat}
                    onProductSuggestion={handleProductSuggestion}
                    onEmotionChange={setEmotionState}
                    showWelcome={showWelcome}
                    onCartOpen={() => setIsCartOpen(true)}
                    onProfileOpen={() => setIsProfileOpen(true)}
                  />
                </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Modals */}
      <ProductDetailModal
        product={selectedProduct}
        isOpen={isProductModalOpen}
        onClose={() => setIsProductModalOpen(false)}
        onPurchase={handlePurchase}
        onAddToCart={addToCart}
      />

      <CartSidebar
        isOpen={isCartOpen}
        onClose={() => setIsCartOpen(false)}
        sessionId={sessionId}
      />

      <UserProfileModal
        isOpen={isProfileOpen}
        onClose={() => setIsProfileOpen(false)}
        sessionId={sessionId}
      />


    </div>
  );
}
