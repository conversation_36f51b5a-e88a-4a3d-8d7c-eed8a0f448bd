-- AGXexperience Store - Fix Category Column (Optional)
-- Run this ONLY if you get "column category does not exist" error

-- Check if category column exists, if not add it
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'category'
    ) THEN
        ALTER TABLE products ADD COLUMN category VARCHAR(100);
        
        -- Update existing products with categories based on their tags
        UPDATE products SET category = 'AI Tools' WHERE 'AI' = ANY(tags);
        UPDATE products SET category = 'Automation' WHERE 'Bot' = ANY(tags) OR 'Telegram' = ANY(tags);
        UPDATE products SET category = 'E-commerce' WHERE 'E-commerce' = ANY(tags) OR 'Store' = ANY(tags);
        UPDATE products SET category = 'Marketing' WHERE 'Social Media' = ANY(tags) OR 'SEO' = ANY(tags);
        UPDATE products SET category = 'Analytics' WHERE 'Analytics' = ANY(tags);
        
        RAISE NOTICE 'Category column added and populated successfully';
    ELSE
        RAISE NOTICE 'Category column already exists';
    END IF;
END $$;

-- Verify the fix
SELECT id, name, category, tags FROM products ORDER BY id;
