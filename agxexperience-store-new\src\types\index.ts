// Product Types
export interface Product {
  _id?: string;
  id?: string; // Support both MongoDB (_id) and Supabase (id) formats
  name: string;
  description: string;
  price: number;
  slug: string;
  image_url: string;
  tags: string[];
  created_at: Date;
}

// User Types
export interface User {
  _id?: string;
  email: string;
  password_hash: string;
  role: 'admin' | 'user';
  created_at: Date;
}

// Message Types
export interface Message {
  _id?: string;
  session_id: string;
  user_message: string;
  ai_response: string;
  timestamp: Date;
  emotion_state: EmotionState;
}

// Order Types
export interface Order {
  _id?: string;
  product_id: string;
  stripe_session_id: string;
  status: 'pending' | 'completed' | 'failed';
  created_at: Date;
}

// User Profile Types - Sistema memoria AURORA
export interface UserProfile {
  _id?: string;
  session_id: string;
  interests: string[];
  past_requests: PastRequest[];
  preferences: UserPreferences;
  interaction_count: number;
  last_seen: Date;
  emotional_profile: EmotionalProfile;
}

export interface PastRequest {
  request: string;
  timestamp: Date;
  response_satisfaction?: number;
}

export interface UserPreferences {
  communication_style: 'formal' | 'casual';
  product_categories: string[];
  price_range: 'premium' | 'budget' | 'any';
}

export interface EmotionalProfile {
  current_mood: 'curious' | 'excited' | 'focused' | 'discovery';
  engagement_level: number; // 0-1
  conversation_depth: 'surface' | 'detailed';
}

// Emotion State Types per AURORA
export type EmotionState = 'curiosity' | 'excitement' | 'focus' | 'discovery';

// AI Chat Types
export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  emotion_state?: EmotionState;
}

export interface ChatSession {
  session_id: string;
  messages: ChatMessage[];
  user_profile?: UserProfile;
}

// Audio Types (432Hz)
export interface AudioFeatures {
  voice_frequency_analysis: boolean;
  real_time_visualization: boolean;
  background_adaptation: boolean;
  emotional_resonance: boolean;
  frequency: number; // 432Hz
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Stripe Types
export interface CheckoutSession {
  product_id: string;
  success_url: string;
  cancel_url: string;
}

// Cart Types
export interface CartItem {
  _id?: string;
  product: Product;
  quantity: number;
  addedAt: Date;
}

export interface Cart {
  _id?: string;
  user_id?: string;
  session_id: string;
  items: CartItem[];
  total: number;
  created_at: Date;
  updated_at: Date;
}

// User Profile Types
export interface UserAddress {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  isDefault?: boolean;
}

export interface UserProfile {
  _id?: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  addresses: UserAddress[];
  orders: string[]; // Order IDs
  preferences: {
    newsletter: boolean;
    notifications: boolean;
    language: string;
  };
  created_at: Date;
  updated_at: Date;
}

// Order Types
export interface Order {
  _id?: string;
  user_id?: string;
  session_id: string;
  items: CartItem[];
  total: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  shipping_address: UserAddress;
  billing_address: UserAddress;
  stripe_session_id?: string;
  tracking_number?: string;
  created_at: Date;
  updated_at: Date;
}
