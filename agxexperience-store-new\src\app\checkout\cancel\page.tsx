'use client';

import { motion } from 'framer-motion';
import { XCircle, ArrowLeft, ShoppingCart, MessageCircle } from 'lucide-react';
import Link from 'next/link';

export default function CheckoutCancel() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50">
      <div className="container mx-auto px-4 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-2xl mx-auto text-center"
        >
          {/* Cancel Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="mb-8"
          >
            <XCircle className="w-20 h-20 text-red-500 mx-auto mb-4" />
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              Pagamento Annullato
            </h1>
            <p className="text-xl text-gray-600">
              Il tuo ordine non è stato completato
            </p>
          </motion.div>

          {/* Message */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              Cosa è successo?
            </h2>
            <p className="text-gray-600 mb-6">
              Hai annullato il processo di pagamento. I tuoi prodotti sono ancora disponibili 
              nel carrello e puoi completare l'acquisto quando vuoi.
            </p>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-blue-800 text-sm">
                💡 <strong>Suggerimento:</strong> Se hai riscontrato problemi durante il pagamento, 
                il nostro team di supporto è qui per aiutarti.
              </p>
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 mb-8"
          >
            <Link
              href="/shop"
              className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center"
            >
              <ShoppingCart className="w-5 h-5 mr-2" />
              Riprova l'Acquisto
            </Link>
            
            <Link
              href="/"
              className="flex-1 bg-white border-2 border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-50 transition-all duration-200 flex items-center justify-center"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Torna alla Home
            </Link>
          </motion.div>

          {/* Support Options */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="bg-white rounded-2xl shadow-lg p-8"
          >
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Hai bisogno di aiuto?
            </h2>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div className="text-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                <MessageCircle className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                <h3 className="font-semibold text-gray-900 mb-1">Chat Live</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Parla con un nostro esperto
                </p>
                <button className="text-blue-600 hover:text-blue-700 font-semibold text-sm">
                  Avvia Chat
                </button>
              </div>
              
              <div className="text-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                <MessageCircle className="w-8 h-8 text-green-600 mx-auto mb-2" />
                <h3 className="font-semibold text-gray-900 mb-1">Email Support</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Risposta entro 24 ore
                </p>
                <Link 
                  href="mailto:<EMAIL>"
                  className="text-green-600 hover:text-green-700 font-semibold text-sm"
                >
                  Invia Email
                </Link>
              </div>
            </div>
          </motion.div>

          {/* FAQ Link */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
            className="mt-8"
          >
            <p className="text-gray-600">
              Domande frequenti?{' '}
              <Link href="/faq" className="text-blue-600 hover:text-blue-700 font-semibold">
                Consulta le FAQ
              </Link>
            </p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}
