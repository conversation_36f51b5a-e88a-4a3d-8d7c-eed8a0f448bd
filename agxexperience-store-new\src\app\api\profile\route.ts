import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { UserProfile, ApiResponse } from '@/types';

// GET - Fetch user profile
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');
    const session_id = searchParams.get('session_id');

    if (!email && !session_id) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Email or Session ID is required'
      }, { status: 400 });
    }

    const db = await getDatabase();
    
    let profile;
    if (email) {
      profile = await db.collection(COLLECTIONS.USER_PROFILES)
        .findOne({ email });
    } else {
      profile = await db.collection(COLLECTIONS.USER_PROFILES)
        .findOne({ session_id });
    }

    if (!profile) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Profile not found'
      }, { status: 404 });
    }

    return NextResponse.json<ApiResponse<UserProfile>>({
      success: true,
      data: profile
    });

  } catch (error) {
    console.error('Profile GET Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to fetch profile'
    }, { status: 500 });
  }
}

// POST - Create user profile
export async function POST(request: NextRequest) {
  try {
    const profileData = await request.json();
    
    const { email, firstName, lastName, phone, addresses, preferences } = profileData;
    
    if (!email || !firstName || !lastName) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Email, firstName, and lastName are required'
      }, { status: 400 });
    }

    const db = await getDatabase();
    
    // Check if profile already exists
    const existingProfile = await db.collection(COLLECTIONS.USER_PROFILES)
      .findOne({ email });
    
    if (existingProfile) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Profile with this email already exists'
      }, { status: 409 });
    }

    const newProfile: UserProfile = {
      email,
      firstName,
      lastName,
      phone: phone || '',
      addresses: addresses || [],
      orders: [],
      preferences: {
        newsletter: preferences?.newsletter || false,
        notifications: preferences?.notifications || true,
        language: preferences?.language || 'it'
      },
      created_at: new Date(),
      updated_at: new Date()
    };

    const result = await db.collection(COLLECTIONS.USER_PROFILES).insertOne(newProfile);
    
    return NextResponse.json<ApiResponse<UserProfile>>({
      success: true,
      data: { ...newProfile, _id: result.insertedId.toString() },
      message: 'Profile created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Profile POST Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to create profile'
    }, { status: 500 });
  }
}

// PUT - Update user profile
export async function PUT(request: NextRequest) {
  try {
    const { email, ...updateData } = await request.json();
    
    if (!email) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Email is required'
      }, { status: 400 });
    }

    const db = await getDatabase();
    
    const updateFields = {
      ...updateData,
      updated_at: new Date()
    };

    const result = await db.collection(COLLECTIONS.USER_PROFILES).updateOne(
      { email },
      { $set: updateFields }
    );

    if (result.matchedCount === 0) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Profile not found'
      }, { status: 404 });
    }

    const updatedProfile = await db.collection(COLLECTIONS.USER_PROFILES)
      .findOne({ email });

    return NextResponse.json<ApiResponse<UserProfile>>({
      success: true,
      data: updatedProfile,
      message: 'Profile updated successfully'
    });

  } catch (error) {
    console.error('Profile PUT Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to update profile'
    }, { status: 500 });
  }
}

// DELETE - Delete user profile
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');

    if (!email) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Email is required'
      }, { status: 400 });
    }

    const db = await getDatabase();
    
    const result = await db.collection(COLLECTIONS.USER_PROFILES).deleteOne({ email });

    if (result.deletedCount === 0) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Profile not found'
      }, { status: 404 });
    }

    return NextResponse.json<ApiResponse>({
      success: true,
      message: 'Profile deleted successfully'
    });

  } catch (error) {
    console.error('Profile DELETE Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to delete profile'
    }, { status: 500 });
  }
}
