'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { Product } from '@/types';

interface ProductCardProps {
  product: Product;
  index?: number;
  variant?: 'compact' | 'shop' | 'carousel';
  onProductSelect?: (product: Product) => void;
}

export default function ProductCard({ 
  product, 
  index = 0, 
  variant = 'shop',
  onProductSelect 
}: ProductCardProps) {
  const router = useRouter();

  const handleCardClick = () => {
    if (onProductSelect) {
      onProductSelect(product);
    } else {
      router.push(`/products/${product.slug}`);
    }
  };

  const handleDetailsClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    router.push(`/products/${product.slug}`);
  };

  // Variant styles
  const getCardStyles = () => {
    switch (variant) {
      case 'compact':
        return {
          container: "group relative bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm rounded-lg p-2 border border-white/10 hover:border-purple-500/50 transition-all cursor-pointer overflow-hidden",
          aspectRatio: { aspectRatio: '1/1.1' },
          imageHeight: "h-12",
          titleSize: "text-xs",
          priceSize: "text-sm"
        };
      case 'carousel':
        return {
          container: "group relative bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/10 hover:border-purple-500/50 transition-all cursor-pointer overflow-hidden",
          aspectRatio: { aspectRatio: '1/1.3' },
          imageHeight: "h-32",
          titleSize: "text-sm",
          priceSize: "text-lg"
        };
      case 'shop':
      default:
        return {
          container: "group relative bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/10 hover:border-purple-500/50 transition-all cursor-pointer overflow-hidden",
          aspectRatio: { aspectRatio: '1/1.4' },
          imageHeight: "h-20",
          titleSize: "text-sm",
          priceSize: "text-lg"
        };
    }
  };

  const styles = getCardStyles();

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ delay: index * 0.05 }}
      whileHover={{ 
        scale: 1.05, 
        y: -2,
        boxShadow: '0 10px 25px rgba(142, 45, 226, 0.3)'
      }}
      className={styles.container}
      style={styles.aspectRatio}
      onClick={handleCardClick}
    >
      {/* Neural Glow Effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      
      {/* Product Image Zone - Allargata */}
      <div className={`w-full ${styles.imageHeight} bg-gradient-to-br from-purple-500/20 to-blue-500/20 rounded-md mb-3 flex items-center justify-center relative overflow-hidden`}>
        <div className={`font-bold text-white/80 ${variant === 'shop' ? 'text-3xl' : variant === 'carousel' ? 'text-4xl' : 'text-xl'}`}>
          {product.name.charAt(0)}
        </div>
        <div className="absolute top-1 right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse" />
        
        {/* Pulsante Dettagli Overlay - Sempre Visibile */}
        <motion.button
          initial={{ opacity: 0, scale: 0.8 }}
          whileHover={{ opacity: 1, scale: 1 }}
          whileTap={{ scale: 0.9 }}
          onClick={handleDetailsClick}
          className="absolute inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300"
        >
          <div className="flex flex-col items-center space-y-1">
            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            <span className="text-xs text-white font-medium">Dettagli</span>
          </div>
        </motion.button>
      </div>

      {/* Info Sezione */}
      <div className="space-y-2 flex-1">
        <h3 className={`font-semibold text-white line-clamp-2 leading-tight group-hover:text-purple-300 transition-colors ${styles.titleSize}`}>
          {product.name}
        </h3>
        
        {/* Price e Action */}
        <div className="flex items-center justify-between">
          <span className={`font-bold text-purple-400 ${styles.priceSize}`}>
            €{product.price}
          </span>
          <motion.div
            whileHover={{ rotate: 90, scale: 1.1 }}
            className="w-6 h-6 bg-purple-500/30 rounded-full flex items-center justify-center"
          >
            <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </motion.div>
        </div>

        {/* Tag Principale */}
        {product.tags[0] && variant !== 'compact' && (
          <span className="inline-block px-2 py-1 bg-purple-500/20 text-purple-300 text-xs rounded-md">
            {product.tags[0]}
          </span>
        )}
      </div>

      {/* Hover Border Effect */}
      <div className="absolute inset-0 border border-purple-500/0 group-hover:border-purple-500/30 rounded-lg transition-all duration-300" />
    </motion.div>
  );
}
