import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin, TABLES } from '@/lib/supabase';
import { Cart, CartItem, Product, ApiResponse } from '@/types';

// GET - Fetch cart
export async function GET(request: NextRequest) {
  // Extract session_id outside try-catch for error handling access
  const { searchParams } = new URL(request.url);
  const session_id = searchParams.get('session_id');

  if (!session_id) {
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Session ID is required'
    }, { status: 400 });
  }

  try {

    // Get cart items with product details from Supabase
    const { data: cartItems, error } = await supabaseAdmin
      .from(TABLES.CART)
      .select(`
        id,
        session_id,
        product_id,
        quantity,
        created_at,
        updated_at,
        products (
          id,
          name,
          price,
          slug
        )
      `)
      .eq('session_id', session_id);

    if (error) {
      throw error;
    }

    // If no cart items, return empty cart
    if (!cartItems || cartItems.length === 0) {
      return NextResponse.json<ApiResponse<Cart>>({
        success: true,
        data: {
          session_id,
          items: [],
          total: 0,
          created_at: new Date(),
          updated_at: new Date()
        }
      });
    }

    // Calculate total
    const total = cartItems.reduce((sum, item: any) =>
      sum + (item.products.price * item.quantity), 0);

    // Format response
    const formattedCart = {
      session_id,
      items: cartItems.map((item: any) => ({
        id: item.id,
        product_id: item.product_id,
        quantity: item.quantity,
        product: {
          _id: item.products.id,
          name: item.products.name,
          price: item.products.price,
          slug: item.products.slug
        }
      })),
      total,
      created_at: cartItems[0]?.created_at || new Date(),
      updated_at: new Date()
    };

    return NextResponse.json<ApiResponse<Cart>>({
      success: true,
      data: formattedCart
    });

  } catch (error) {
    console.error('Cart GET Error:', error);
    console.log('🔄 Using empty cart for development...');

    // Return empty cart when MongoDB is not available
    return NextResponse.json<ApiResponse<Cart>>({
      success: true,
      data: {
        session_id,
        items: [],
        total: 0,
        created_at: new Date(),
        updated_at: new Date()
      },
      message: 'Using development mode - MongoDB connection failed'
    });
  }
}

// POST - Add item to cart
export async function POST(request: NextRequest) {
  try {
    const { session_id, product_id, quantity = 1 } = await request.json();

    if (!session_id || !product_id) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Session ID and Product ID are required'
      }, { status: 400 });
    }

    // Get product details from Supabase - cerca sia per id che per slug
    let { data: product, error: productError } = await supabaseAdmin
      .from(TABLES.PRODUCTS)
      .select('*')
      .eq('id', product_id)
      .single();

    // Se non trovato per ID, prova con slug
    if (productError || !product) {
      const { data: productBySlug, error: slugError } = await supabaseAdmin
        .from(TABLES.PRODUCTS)
        .select('*')
        .eq('slug', product_id)
        .single();

      if (!slugError && productBySlug) {
        product = productBySlug;
        productError = null;
      }
    }

    if (productError || !product) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Product not found'
      }, { status: 404 });
    }

    // Usa l'ID reale del prodotto trovato
    const realProductId = product.id;

    // Check if item already exists in cart
    const { data: existingItem } = await supabaseAdmin
      .from(TABLES.CART)
      .select('*')
      .eq('session_id', session_id)
      .eq('product_id', realProductId)
      .single();

    if (existingItem) {
      // Update existing item quantity
      const { error: updateError } = await supabaseAdmin
        .from(TABLES.CART)
        .update({
          quantity: existingItem.quantity + quantity,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingItem.id);

      if (updateError) {
        throw updateError;
      }
    } else {
      // Add new item to cart
      const { error: insertError } = await supabaseAdmin
        .from(TABLES.CART)
        .insert({
          session_id,
          product_id: realProductId,
          quantity
        });

      if (insertError) {
        throw insertError;
      }
    }

    // Get updated cart to return
    const { data: updatedCartItems } = await supabaseAdmin
      .from(TABLES.CART)
      .select(`
        id,
        session_id,
        product_id,
        quantity,
        created_at,
        updated_at,
        products (
          id,
          name,
          price,
          slug
        )
      `)
      .eq('session_id', session_id);

    // Calculate total
    const total = updatedCartItems?.reduce((sum, item: any) =>
      sum + (item.products.price * item.quantity), 0) || 0;

    return NextResponse.json<ApiResponse>({
      success: true,
      message: 'Item added to cart',
      data: {
        session_id,
        items: updatedCartItems || [],
        total
      }
    });

  } catch (error) {
    console.error('Cart POST Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to add item to cart'
    }, { status: 500 });
  }
}

// PUT - Update cart item quantity
export async function PUT(request: NextRequest) {
  try {
    const { session_id, product_id, quantity } = await request.json();

    if (!session_id || !product_id || quantity < 0) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Invalid parameters'
      }, { status: 400 });
    }

    const db = await getDatabase();
    
    const cart = await db.collection(COLLECTIONS.CARTS)
      .findOne({ session_id });

    if (!cart) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Cart not found'
      }, { status: 404 });
    }

    if (quantity === 0) {
      // Remove item
      cart.items = cart.items.filter((item: any) => item.product_id !== product_id);
    } else {
      // Update quantity
      const itemIndex = cart.items.findIndex((item: any) => item.product_id === product_id);
      if (itemIndex >= 0) {
        cart.items[itemIndex].quantity = quantity;
      }
    }

    // Recalculate total
    cart.total = await calculateCartTotal(db, cart.items);
    cart.updated_at = new Date();

    // Save cart
    await db.collection(COLLECTIONS.CARTS).replaceOne(
      { session_id },
      cart
    );

    return NextResponse.json<ApiResponse>({
      success: true,
      message: 'Cart updated',
      data: cart
    });

  } catch (error) {
    console.error('Cart PUT Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to update cart'
    }, { status: 500 });
  }
}

// DELETE - Clear cart
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const session_id = searchParams.get('session_id');

    if (!session_id) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Session ID is required'
      }, { status: 400 });
    }

    const db = await getDatabase();
    
    await db.collection(COLLECTIONS.CARTS).deleteOne({ session_id });

    return NextResponse.json<ApiResponse>({
      success: true,
      message: 'Cart cleared'
    });

  } catch (error) {
    console.error('Cart DELETE Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to clear cart'
    }, { status: 500 });
  }
}

// Helper function to calculate cart total
async function calculateCartTotal(db: any, items: any[]): Promise<number> {
  let total = 0;
  
  for (const item of items) {
    const product = await db.collection(COLLECTIONS.PRODUCTS)
      .findOne({ _id: item.product_id });
    
    if (product) {
      total += product.price * item.quantity;
    }
  }
  
  return total;
}
