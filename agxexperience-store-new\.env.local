# Together.ai API Configuration
TOGETHER_API_KEY=b4e1dd7a61cc0f0acf05f86b8d7fbe6c1648e6850f9fd2db5a32facb2f87c6de
TOGETHER_MODEL=meta-llama/Llama-3.3-70B-Instruct-Turbo-Free
TOGETHER_ENDPOINT=https://api.together.ai/models/meta-llama/Llama-3.3-70B-Instruct-Turbo-Free

# Stripe Configuration (TEST KEYS - for development)
# SOSTITUISCI CON LE TUE CHIAVI DI TEST DA https://dashboard.stripe.com/test/apikeys
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_placeholder_replace_with_your_test_key
STRIPE_SECRET_KEY=sk_test_placeholder_replace_with_your_test_key
STRIPE_WEBHOOK_SECRET=whsec_placeholder_replace_with_your_webhook_secret
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://pbpzonzzkbvkuzulmdsi.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBicHpvbnp6a2J2a3V6dWxtZHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzODM4NzEsImV4cCI6MjA2ODk1OTg3MX0.WJh_4ztuXrmV3pXjZ8_4XOgK7Nb6ApSs-zs-m1vYqPo
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBicHpvbnp6a2J2a3V6dWxtZHNpIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzM4Mzg3MSwiZXhwIjoyMDY4OTU5ODcxfQ.d4cX-fnQPWpLnNI00RAAEMonfkjv277wnx5yT8jlr1Y
MONGODB_USERNAME=agtechdesigne
MONGODB_PASSWORD=usNjilJyMQQftSAO

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_here

# Audio Configuration (432Hz)
AUDIO_FREQUENCY=432
