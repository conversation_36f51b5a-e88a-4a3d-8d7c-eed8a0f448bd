'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { CheckCircle, Download, Mail, ArrowRight, Home } from 'lucide-react';
import Link from 'next/link';

export default function CheckoutSuccess() {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id');
  const [orderDetails, setOrderDetails] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (sessionId) {
      // Qui potresti fare una chiamata API per ottenere i dettagli dell'ordine
      // Per ora simuliamo i dati
      setTimeout(() => {
        setOrderDetails({
          id: sessionId,
          total: 299.00,
          items: [
            { name: 'AI Content Generator', price: 299.00 }
          ],
          email: '<EMAIL>'
        });
        setIsLoading(false);
      }, 1000);
    } else {
      setIsLoading(false);
    }
  }, [sessionId]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Elaborazione del pagamento...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-2xl mx-auto"
        >
          {/* Success Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="text-center mb-8"
          >
            <CheckCircle className="w-20 h-20 text-green-500 mx-auto mb-4" />
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              Pagamento Completato!
            </h1>
            <p className="text-xl text-gray-600">
              Grazie per il tuo acquisto
            </p>
          </motion.div>

          {/* Order Details */}
          {orderDetails && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-white rounded-2xl shadow-lg p-8 mb-8"
            >
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                Dettagli Ordine
              </h2>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b">
                  <span className="text-gray-600">ID Ordine:</span>
                  <span className="font-mono text-sm bg-gray-100 px-3 py-1 rounded">
                    {orderDetails.id.slice(0, 8)}...
                  </span>
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-semibold text-gray-900">Prodotti:</h3>
                  {orderDetails.items.map((item: any, index: number) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-gray-700">{item.name}</span>
                      <span className="font-semibold">€{item.price}</span>
                    </div>
                  ))}
                </div>
                
                <div className="flex justify-between items-center py-3 border-t font-semibold text-lg">
                  <span>Totale:</span>
                  <span className="text-blue-600">€{orderDetails.total}</span>
                </div>
              </div>
            </motion.div>
          )}

          {/* Next Steps */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">
              Prossimi Passi
            </h2>
            
            <div className="space-y-4">
              <div className="flex items-start space-x-4">
                <Mail className="w-6 h-6 text-blue-600 mt-1" />
                <div>
                  <h3 className="font-semibold text-gray-900">Email di Conferma</h3>
                  <p className="text-gray-600">
                    Riceverai una email di conferma con i dettagli dell'ordine e le istruzioni per l'accesso.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <Download className="w-6 h-6 text-green-600 mt-1" />
                <div>
                  <h3 className="font-semibold text-gray-900">Accesso ai Prodotti</h3>
                  <p className="text-gray-600">
                    I tuoi prodotti digitali saranno disponibili nel tuo account entro 5 minuti.
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="flex flex-col sm:flex-row gap-4"
          >
            <Link
              href="/account"
              className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center"
            >
              <Download className="w-5 h-5 mr-2" />
              Accedi ai Prodotti
              <ArrowRight className="w-5 h-5 ml-2" />
            </Link>
            
            <Link
              href="/"
              className="flex-1 bg-white border-2 border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-50 transition-all duration-200 flex items-center justify-center"
            >
              <Home className="w-5 h-5 mr-2" />
              Torna alla Home
            </Link>
          </motion.div>

          {/* Support */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
            className="text-center mt-12"
          >
            <p className="text-gray-600">
              Hai bisogno di aiuto?{' '}
              <Link href="/support" className="text-blue-600 hover:text-blue-700 font-semibold">
                Contatta il supporto
              </Link>
            </p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}
