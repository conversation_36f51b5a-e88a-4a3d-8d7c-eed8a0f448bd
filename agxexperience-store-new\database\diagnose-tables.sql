-- AGXexperience Store - Database Diagnosis
-- Run this to see what tables and columns exist

-- Show all tables in public schema
SELECT 
    'EXISTING TABLES' as info,
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public'
AND table_name NOT LIKE 'pg_%'
AND table_name NOT LIKE 'sql_%'
ORDER BY table_name;

-- Show orders table structure if it exists
SELECT 
    'ORDERS TABLE COLUMNS' as info,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'orders'
ORDER BY ordinal_position;

-- Show all indexes on orders table
SELECT 
    'ORDERS TABLE INDEXES' as info,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'orders' 
AND schemaname = 'public';

-- Show all policies on orders table
SELECT 
    'ORDERS TABLE POLICIES' as info,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'orders' 
AND schemaname = 'public';

-- Check if specific columns exist
SELECT 
    'COLUMN EXISTENCE CHECK' as info,
    'customer_email' as column_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'orders' 
            AND column_name = 'customer_email'
        ) THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as status
UNION ALL
SELECT 
    'COLUMN EXISTENCE CHECK' as info,
    'customer_name' as column_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'orders' 
            AND column_name = 'customer_name'
        ) THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as status
UNION ALL
SELECT 
    'COLUMN EXISTENCE CHECK' as info,
    'total_amount' as column_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'orders' 
            AND column_name = 'total_amount'
        ) THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as status;

-- Show sample data from orders if table exists and has data
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders') THEN
        RAISE NOTICE 'Orders table exists, checking for data...';
        PERFORM * FROM orders LIMIT 1;
        RAISE NOTICE 'Orders table has data';
    ELSE
        RAISE NOTICE 'Orders table does not exist';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Orders table exists but may be empty or have issues: %', SQLERRM;
END $$;
