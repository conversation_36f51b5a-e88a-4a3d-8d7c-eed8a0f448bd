import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { supabaseAdmin } from '@/lib/supabase';

// Inizializza Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2024-06-20',
});

export async function POST(request: NextRequest) {
  try {
    const { items, totalAmount, userEmail } = await request.json();

    // Valida i dati ricevuti
    if (!items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { error: 'Carrello vuoto o non valido' },
        { status: 400 }
      );
    }

    // Crea i line items per Stripe
    const lineItems = items.map((item: any) => ({
      price_data: {
        currency: 'eur',
        product_data: {
          name: item.name,
          description: `Prodotto digitale AGXexperience - ${item.name}`,
          images: item.image_url ? [item.image_url] : [],
          metadata: {
            product_id: item.id,
          },
        },
        unit_amount: Math.round(item.price * 100), // Stripe usa i centesimi
      },
      quantity: item.quantity,
    }));

    // Crea la sessione di checkout Stripe
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: lineItems,
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/checkout/cancel`,
      metadata: {
        totalAmount: totalAmount.toString(),
        itemCount: items.length.toString(),
        type: 'cart_checkout',
        userEmail: userEmail || '',
        items: JSON.stringify(items.map(item => ({
          id: item.id,
          name: item.name,
          price: item.price,
          quantity: item.quantity
        })))
      },
      billing_address_collection: 'required',
      shipping_address_collection: {
        allowed_countries: ['IT', 'CH', 'FR', 'DE', 'ES', 'AT'],
      },
      customer_creation: 'always',
      invoice_creation: {
        enabled: true,
      },
      automatic_tax: {
        enabled: false,
      },
      allow_promotion_codes: true,
    });

    return NextResponse.json({ 
      success: true,
      url: session.url,
      sessionId: session.id 
    });

  } catch (error) {
    console.error('Errore durante la creazione della sessione Stripe:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Errore interno del server durante il checkout' 
      },
      { status: 500 }
    );
  }
}

// Webhook per gestire gli eventi Stripe
export async function PUT(request: NextRequest) {
  try {
    const sig = request.headers.get('stripe-signature');
    const body = await request.text();

    if (!sig) {
      return NextResponse.json(
        { error: 'Signature mancante' },
        { status: 400 }
      );
    }

    // Verifica la signature del webhook (solo se hai configurato il webhook secret)
    let event;
    try {
      event = stripe.webhooks.constructEvent(
        body,
        sig,
        process.env.STRIPE_WEBHOOK_SECRET || ''
      );
    } catch (err) {
      console.error('Errore verifica webhook signature:', err);
      return NextResponse.json(
        { error: 'Signature non valida' },
        { status: 400 }
      );
    }

    // Gestisci gli eventi Stripe
    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object as Stripe.Checkout.Session;
        
        // Salva l'ordine nel database
        await saveOrderToDatabase(session);
        
        console.log('Pagamento completato:', session.id);
        break;

      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        console.log('PaymentIntent succeeded:', paymentIntent.id);
        break;

      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object as Stripe.PaymentIntent;
        console.log('PaymentIntent failed:', failedPayment.id);
        break;

      default:
        console.log(`Evento non gestito: ${event.type}`);
    }

    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Errore webhook Stripe:', error);
    return NextResponse.json(
      { error: 'Errore webhook' },
      { status: 400 }
    );
  }
}

// Funzione per salvare l'ordine nel database
async function saveOrderToDatabase(session: Stripe.Checkout.Session) {
  try {
    // Estrai i dati dalla sessione
    const metadata = session.metadata;
    const customerDetails = session.customer_details;
    
    if (!metadata || !customerDetails) {
      console.error('Dati sessione incompleti');
      return;
    }

    // Prepara i dati dell'ordine
    const orderData = {
      stripe_session_id: session.id,
      customer_email: customerDetails.email,
      customer_name: customerDetails.name,
      total_amount: session.amount_total ? session.amount_total / 100 : 0,
      status: 'completed',
      payment_intent_id: session.payment_intent as string,
      created_at: new Date().toISOString(),
      metadata: {
        stripe_metadata: metadata,
        customer_details: customerDetails,
        billing_address: customerDetails.address,
        shipping_address: session.shipping_details?.address
      }
    };

    // Salva l'ordine principale
    const { data: order, error: orderError } = await supabaseAdmin
      .from('orders')
      .insert([orderData])
      .select()
      .single();

    if (orderError) {
      console.error('Errore salvataggio ordine:', orderError);
      return;
    }

    console.log('Ordine salvato:', order.id);

    // Salva gli order items se presenti nei metadata
    if (metadata.items) {
      try {
        const items = JSON.parse(metadata.items);
        const orderItems = items.map((item: any) => ({
          order_id: order.id,
          product_id: item.id,
          product_name: item.name,
          product_price: item.price,
          quantity: item.quantity,
          total_price: item.price * item.quantity
        }));

        const { error: itemsError } = await supabaseAdmin
          .from('order_items')
          .insert(orderItems);

        if (itemsError) {
          console.error('Errore salvataggio order items:', itemsError);
        } else {
          console.log('Order items salvati:', orderItems.length);
        }
      } catch (parseError) {
        console.error('Errore parsing items:', parseError);
      }
    }

  } catch (error) {
    console.error('Errore durante il salvataggio dell\'ordine:', error);
  }
}
