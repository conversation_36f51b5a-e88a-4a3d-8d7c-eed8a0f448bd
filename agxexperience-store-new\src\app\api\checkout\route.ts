import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { ApiResponse } from '@/types';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', {
  apiVersion: '2024-06-20',
});

export async function POST(request: NextRequest) {
  try {
    const { product_id, productId, items, totalAmount } = await request.json();

    // Gestisci sia il nuovo formato (carrello) che il vecchio (singolo prodotto)
    if (items && Array.isArray(items) && items.length > 0) {
      // Nuovo formato: carrello con più prodotti
      return await handleCartCheckout(items, totalAmount);
    } else if (product_id || productId) {
      // Vecchio formato: singolo prodotto
      return await handleSingleProductCheckout(product_id || productId);
    } else {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Product ID or items are required'
      }, { status: 400 });
    }
  } catch (error) {
    console.error('Errore durante il checkout:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Errore interno del server durante il checkout'
    }, { status: 500 });
  }
}

// Gestisce il checkout del carrello
async function handleCartCheckout(items: any[], totalAmount: number) {
  try {
    // Crea i line items per Stripe
    const lineItems = items.map((item: any) => ({
      price_data: {
        currency: 'eur',
        product_data: {
          name: item.name,
          description: `Prodotto digitale AGXexperience - ${item.name}`,
          images: item.image_url ? [item.image_url] : [],
        },
        unit_amount: Math.round(item.price * 100), // Stripe usa i centesimi
      },
      quantity: item.quantity,
    }));

    // Crea la sessione di checkout Stripe
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: lineItems,
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/checkout/cancel`,
      metadata: {
        totalAmount: totalAmount.toString(),
        itemCount: items.length.toString(),
        type: 'cart_checkout'
      },
      billing_address_collection: 'required',
      customer_creation: 'always',
    });

    return NextResponse.json({
      success: true,
      url: session.url,
      sessionId: session.id
    });

  } catch (error) {
    console.error('Errore durante la creazione della sessione Stripe (carrello):', error);
    throw error;
  }
}

// Gestisce il checkout di un singolo prodotto (compatibilità)
async function handleSingleProductCheckout(productId: string) {
  try {
    // Get product details from Supabase
    const { data: product, error } = await supabaseAdmin
      .from('products')
      .select('*')
      .eq('id', productId)
      .single();

    if (error || !product) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Product not found'
      }, { status: 404 });
    }

    // Create Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: product.name,
              description: product.description || 'Prodotto digitale AGXexperience',
              images: product.image_url ? [product.image_url] : [],
            },
            unit_amount: Math.round(product.price * 100), // Convert to cents
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/checkout/cancel`,
      metadata: {
        product_id: productId,
        product_name: product.name,
        type: 'single_product'
      },
      billing_address_collection: 'required',
      customer_creation: 'always',
    });

    // Save order to Supabase
    const { error: orderError } = await supabaseAdmin
      .from('orders')
      .insert([{
        stripe_session_id: session.id,
        customer_email: '', // Will be filled by webhook
        total_amount: product.price,
        status: 'pending',
        created_at: new Date().toISOString(),
        metadata: {
          product_id: productId,
          product_name: product.name,
          type: 'single_product'
        }
      }]);

    if (orderError) {
      console.error('Errore salvataggio ordine:', orderError);
    }

    return NextResponse.json<ApiResponse>({
      success: true,
      data: {
        checkout_url: session.url,
        session_id: session.id,
        product_name: product.name,
        amount: product.price
      }
    });

  } catch (error) {
    console.error('Stripe Checkout Error (single product):', error);
    throw error;
  }
}
