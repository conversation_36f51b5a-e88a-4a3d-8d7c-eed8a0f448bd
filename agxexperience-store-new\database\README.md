# AGXexperience Store - Database Setup

## 🗄️ Supabase Database Setup Instructions

### Option A: Quick Fix (For Current Error)
1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Copy and paste the content of `quick-fix-orders.sql`
4. Click **Run** to execute
5. Then run `02-sample-data.sql` for sample data

### Option B: Ultra Safe Setup (Complete)
1. Copy and paste the content of `01-ultra-safe-setup.sql`
2. Click **Run** to execute
3. Then run `02-sample-data.sql`

### Option C: If You Get Column Errors
1. First run `diagnose-tables.sql` to see what exists
2. Then run `fix-customer-email.sql` to fix column issues
3. Finally run `01-ultra-safe-setup.sql`

### Option C: Complete Reset
1. First run `00-reset-database.sql` to clean everything
2. Then run `01-ultra-safe-setup.sql`
3. This will give you a completely fresh start

The setup will create:
- All required tables (user_profiles, orders, products, etc.)
- Indexes for performance
- Row Level Security policies
- Basic permissions
- Triggers for auto-updates

### Step 2: Sample Data
1. After Step 1 completes successfully
2. Copy and paste the content of `02-sample-data.sql`
3. Click **Run** to execute

This will insert:
- 5 sample user profiles
- 8 sample products
- 10 sample orders with order items
- Analytics events
- System metrics
- Chat messages

### Step 3: Verify Setup
After running both scripts, verify in Supabase:

1. **Table Editor** → Check that all tables exist:
   - user_profiles (5 rows)
   - products (8 rows)
   - orders (10 rows)
   - order_items (10 rows)
   - analytics_events (9 rows)
   - system_metrics (8 rows)
   - messages (4 rows)

2. **API** → Test a query:
   ```sql
   SELECT COUNT(*) FROM orders WHERE status = 'completed';
   ```
   Should return 6 completed orders.

### Step 4: Test Dashboard
1. Go to `http://localhost:3000/admin`
2. The dashboard should now show real data:
   - Total Revenue: €4,092
   - Total Orders: 10
   - Total Users: 5
   - Real charts and metrics

## 🔧 Troubleshooting

### If you get "column does not exist" errors (customer_email, etc.):
1. **QUICK FIX**: Run `fix-customer-email.sql` to fix column issues
2. **DIAGNOSIS**: Run `diagnose-tables.sql` to see what exists
3. **ULTRA SAFE**: Use `01-ultra-safe-setup.sql` - handles all column issues

### If you get "policy already exists" errors:
1. **SOLUTION**: Use `01-ultra-safe-setup.sql` instead of basic setup
2. The ultra safe setup handles all conflicts automatically
3. You can run it multiple times without errors

### If you get "relation does not exist" errors:
1. Make sure you run the setup script first
2. Check that all tables were created in Table Editor
3. If some tables are missing, run the setup script again

### If you get "column category does not exist" error:
1. Run `03-fix-category.sql` to add the missing column
2. Then run `02-sample-data.sql` again

### If you want to start completely fresh:
1. Run `00-reset-database.sql` to clean everything
2. Then run `01-safe-setup.sql` for fresh setup
3. Finally run `02-sample-data.sql` for sample data

### Quick Reset Commands:
```sql
-- Complete reset (WARNING: Deletes all data)
\i 00-reset-database.sql

-- Fresh setup
\i 01-safe-setup.sql

-- Add sample data
\i 02-sample-data.sql
```

### If data doesn't appear in dashboard:
1. Check that `02-sample-data.sql` ran without errors
2. Verify data exists in Table Editor
3. Check browser console for API errors
4. Restart the Next.js development server

### If you need to reset:
```sql
-- WARNING: This will delete all data
DROP TABLE IF EXISTS order_items CASCADE;
DROP TABLE IF EXISTS orders CASCADE;
DROP TABLE IF EXISTS analytics_events CASCADE;
DROP TABLE IF EXISTS system_metrics CASCADE;
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS cart CASCADE;
DROP TABLE IF EXISTS user_profiles CASCADE;
DROP TABLE IF EXISTS products CASCADE;
```

Then run the setup scripts again.

## 📊 Expected Results

After successful setup, your admin dashboard will show:
- **Real Revenue Data**: €4,092 total from 10 orders
- **User Analytics**: 5 users with real interaction data
- **Product Performance**: 8 products with sales data
- **System Metrics**: CPU, memory, response time tracking
- **Real-time Updates**: Live data refreshing every 30 seconds

## 🚀 Production Notes

For production deployment:
1. Update RLS policies for proper security
2. Set up proper authentication
3. Configure environment variables
4. Enable database backups
5. Set up monitoring and alerts

The current setup uses permissive policies (`USING (true)`) for development. In production, implement proper user-based access control.
