-- AGXexperience Store - Complete Table Verification
-- Run this to check the current state of all tables before inserting data

-- 1. Check which tables exist
SELECT 
    '=== EXISTING TABLES ===' as section,
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public'
AND table_name IN ('user_profiles', 'products', 'orders', 'order_items', 'cart', 'analytics_events', 'system_metrics', 'messages')
ORDER BY table_name;

-- 2. Check user_profiles table structure
SELECT 
    '=== USER_PROFILES STRUCTURE ===' as section,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'user_profiles'
ORDER BY ordinal_position;

-- 3. Check products table structure
SELECT 
    '=== PRODUCTS STRUCTURE ===' as section,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'products'
ORDER BY ordinal_position;

-- 4. Check orders table structure
SELECT 
    '=== ORDERS STRUCTURE ===' as section,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'orders'
ORDER BY ordinal_position;

-- 5. Check order_items table structure
SELECT 
    '=== ORDER_ITEMS STRUCTURE ===' as section,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'order_items'
ORDER BY ordinal_position;

-- 6. Check analytics_events table structure
SELECT 
    '=== ANALYTICS_EVENTS STRUCTURE ===' as section,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'analytics_events'
ORDER BY ordinal_position;

-- 7. Check system_metrics table structure
SELECT 
    '=== SYSTEM_METRICS STRUCTURE ===' as section,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'system_metrics'
ORDER BY ordinal_position;

-- 8. Check messages table structure
SELECT 
    '=== MESSAGES STRUCTURE ===' as section,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'messages'
ORDER BY ordinal_position;

-- 9. Check current data counts
DO $$
DECLARE
    user_count INTEGER := 0;
    product_count INTEGER := 0;
    order_count INTEGER := 0;
    order_item_count INTEGER := 0;
    analytics_count INTEGER := 0;
    metrics_count INTEGER := 0;
    message_count INTEGER := 0;
BEGIN
    -- Count existing data
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
        SELECT COUNT(*) INTO user_count FROM user_profiles;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'products') THEN
        SELECT COUNT(*) INTO product_count FROM products;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders') THEN
        SELECT COUNT(*) INTO order_count FROM orders;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'order_items') THEN
        SELECT COUNT(*) INTO order_item_count FROM order_items;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'analytics_events') THEN
        SELECT COUNT(*) INTO analytics_count FROM analytics_events;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_metrics') THEN
        SELECT COUNT(*) INTO metrics_count FROM system_metrics;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages') THEN
        SELECT COUNT(*) INTO message_count FROM messages;
    END IF;
    
    RAISE NOTICE '=== CURRENT DATA COUNTS ===';
    RAISE NOTICE 'User Profiles: %', user_count;
    RAISE NOTICE 'Products: %', product_count;
    RAISE NOTICE 'Orders: %', order_count;
    RAISE NOTICE 'Order Items: %', order_item_count;
    RAISE NOTICE 'Analytics Events: %', analytics_count;
    RAISE NOTICE 'System Metrics: %', metrics_count;
    RAISE NOTICE 'Messages: %', message_count;
END $$;

-- 10. Check for missing tables and suggest actions
DO $$
BEGIN
    RAISE NOTICE '=== MISSING TABLES CHECK ===';
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
        RAISE NOTICE 'MISSING: user_profiles table - need to create it';
    ELSE
        RAISE NOTICE 'OK: user_profiles table exists';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'products') THEN
        RAISE NOTICE 'MISSING: products table - need to create it';
    ELSE
        RAISE NOTICE 'OK: products table exists';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders') THEN
        RAISE NOTICE 'MISSING: orders table - need to create it';
    ELSE
        RAISE NOTICE 'OK: orders table exists';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'order_items') THEN
        RAISE NOTICE 'MISSING: order_items table - need to create it';
    ELSE
        RAISE NOTICE 'OK: order_items table exists';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'analytics_events') THEN
        RAISE NOTICE 'MISSING: analytics_events table - need to create it';
    ELSE
        RAISE NOTICE 'OK: analytics_events table exists';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_metrics') THEN
        RAISE NOTICE 'MISSING: system_metrics table - need to create it';
    ELSE
        RAISE NOTICE 'OK: system_metrics table exists';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages') THEN
        RAISE NOTICE 'MISSING: messages table - need to create it';
    ELSE
        RAISE NOTICE 'OK: messages table exists';
    END IF;
END $$;

-- 11. Final summary
SELECT 
    '=== VERIFICATION COMPLETE ===' as status,
    'Check the output above to see what tables exist and their structure' as next_action;
