/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/cart/route";
exports.ids = ["app/api/cart/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcart%2Froute&page=%2Fapi%2Fcart%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcart%2Froute.ts&appDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcart%2Froute&page=%2Fapi%2Fcart%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcart%2Froute.ts&appDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var G_AGtech_E_commerce_agxexperience_store_new_src_app_api_cart_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/cart/route.ts */ \"(rsc)/./src/app/api/cart/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/cart/route\",\n        pathname: \"/api/cart\",\n        filename: \"route\",\n        bundlePath: \"app/api/cart/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"G:\\\\AGtech\\\\E-commerce\\\\agxexperience-store-new\\\\src\\\\app\\\\api\\\\cart\\\\route.ts\",\n    nextConfigOutput,\n    userland: G_AGtech_E_commerce_agxexperience_store_new_src_app_api_cart_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/cart/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcart%2Froute&page=%2Fapi%2Fcart%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcart%2Froute.ts&appDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/cart/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/cart/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\n// GET - Fetch cart\nasync function GET(request) {\n    // Extract session_id outside try-catch for error handling access\n    const { searchParams } = new URL(request.url);\n    const session_id = searchParams.get('session_id');\n    if (!session_id) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Session ID is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        // Get cart items with product details from Supabase\n        const { data: cartItems, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.TABLES.CART).select(`\n        id,\n        session_id,\n        product_id,\n        quantity,\n        created_at,\n        updated_at,\n        products (\n          id,\n          name,\n          price,\n          slug\n        )\n      `).eq('session_id', session_id);\n        if (error) {\n            throw error;\n        }\n        // If no cart items, return empty cart\n        if (!cartItems || cartItems.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: {\n                    session_id,\n                    items: [],\n                    total: 0,\n                    created_at: new Date(),\n                    updated_at: new Date()\n                }\n            });\n        }\n        // Calculate total\n        const total = cartItems.reduce((sum, item)=>sum + item.products.price * item.quantity, 0);\n        // Format response\n        const formattedCart = {\n            session_id,\n            items: cartItems.map((item)=>({\n                    id: item.id,\n                    product_id: item.product_id,\n                    quantity: item.quantity,\n                    product: {\n                        _id: item.products.id,\n                        name: item.products.name,\n                        price: item.products.price,\n                        slug: item.products.slug\n                    }\n                })),\n            total,\n            created_at: cartItems[0]?.created_at || new Date(),\n            updated_at: new Date()\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: formattedCart\n        });\n    } catch (error) {\n        console.error('Cart GET Error:', error);\n        console.log('🔄 Using empty cart for development...');\n        // Return empty cart when MongoDB is not available\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                session_id,\n                items: [],\n                total: 0,\n                created_at: new Date(),\n                updated_at: new Date()\n            },\n            message: 'Using development mode - MongoDB connection failed'\n        });\n    }\n}\n// POST - Add item to cart\nasync function POST(request) {\n    try {\n        const { session_id, product_id, quantity = 1 } = await request.json();\n        if (!session_id || !product_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Session ID and Product ID are required'\n            }, {\n                status: 400\n            });\n        }\n        // Get product details from Supabase - cerca sia per id che per slug\n        let { data: product, error: productError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.TABLES.PRODUCTS).select('*').eq('id', product_id).single();\n        // I prodotti hanno ID stringa, non UUID, quindi la ricerca per ID dovrebbe funzionare\n        if (productError || !product) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Product not found'\n            }, {\n                status: 404\n            });\n        }\n        // Usa l'ID reale del prodotto trovato\n        const realProductId = product.id;\n        // Debug: verifica il prodotto trovato\n        console.log('🔍 Product found:', {\n            originalId: product_id,\n            realProductId: realProductId,\n            productName: product.name\n        });\n        // Check if item already exists in cart\n        const { data: existingItem } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.TABLES.CART).select('*').eq('session_id', session_id).eq('product_id', realProductId).single();\n        if (existingItem) {\n            // Update existing item quantity\n            const { error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.TABLES.CART).update({\n                quantity: existingItem.quantity + quantity,\n                updated_at: new Date().toISOString()\n            }).eq('id', existingItem.id);\n            if (updateError) {\n                throw updateError;\n            }\n        } else {\n            // Add new item to cart\n            const { error: insertError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.TABLES.CART).insert({\n                session_id,\n                product_id: realProductId,\n                quantity\n            });\n            if (insertError) {\n                throw insertError;\n            }\n        }\n        // Get updated cart to return\n        const { data: updatedCartItems } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.TABLES.CART).select(`\n        id,\n        session_id,\n        product_id,\n        quantity,\n        created_at,\n        updated_at,\n        products (\n          id,\n          name,\n          price,\n          slug\n        )\n      `).eq('session_id', session_id);\n        // Calculate total\n        const total = updatedCartItems?.reduce((sum, item)=>sum + item.products.price * item.quantity, 0) || 0;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Item added to cart',\n            data: {\n                session_id,\n                items: updatedCartItems || [],\n                total\n            }\n        });\n    } catch (error) {\n        console.error('Cart POST Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to add item to cart'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - Update cart item quantity\nasync function PUT(request) {\n    try {\n        const { session_id, product_id, quantity } = await request.json();\n        if (!session_id || !product_id || quantity < 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Invalid parameters'\n            }, {\n                status: 400\n            });\n        }\n        const db = await getDatabase();\n        const cart = await db.collection(COLLECTIONS.CARTS).findOne({\n            session_id\n        });\n        if (!cart) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Cart not found'\n            }, {\n                status: 404\n            });\n        }\n        if (quantity === 0) {\n            // Remove item\n            cart.items = cart.items.filter((item)=>item.product_id !== product_id);\n        } else {\n            // Update quantity\n            const itemIndex = cart.items.findIndex((item)=>item.product_id === product_id);\n            if (itemIndex >= 0) {\n                cart.items[itemIndex].quantity = quantity;\n            }\n        }\n        // Recalculate total\n        cart.total = await calculateCartTotal(db, cart.items);\n        cart.updated_at = new Date();\n        // Save cart\n        await db.collection(COLLECTIONS.CARTS).replaceOne({\n            session_id\n        }, cart);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Cart updated',\n            data: cart\n        });\n    } catch (error) {\n        console.error('Cart PUT Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to update cart'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - Clear cart\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const session_id = searchParams.get('session_id');\n        if (!session_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Session ID is required'\n            }, {\n                status: 400\n            });\n        }\n        const db = await getDatabase();\n        await db.collection(COLLECTIONS.CARTS).deleteOne({\n            session_id\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Cart cleared'\n        });\n    } catch (error) {\n        console.error('Cart DELETE Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to clear cart'\n        }, {\n            status: 500\n        });\n    }\n}\n// Helper function to calculate cart total\nasync function calculateCartTotal(db, items) {\n    let total = 0;\n    for (const item of items){\n        const product = await db.collection(COLLECTIONS.PRODUCTS).findOne({\n            _id: item.product_id\n        });\n        if (product) {\n            total += product.price * item.quantity;\n        }\n    }\n    return total;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/cart/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   getSupabaseClient: () => (/* binding */ getSupabaseClient),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// Supabase configuration\nconst supabaseUrl = \"https://pbpzonzzkbvkuzulmdsi.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBicHpvbnp6a2J2a3V6dWxtZHNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzODM4NzEsImV4cCI6MjA2ODk1OTg3MX0.WJh_4ztuXrmV3pXjZ8_4XOgK7Nb6ApSs-zs-m1vYqPo\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Client per operazioni pubbliche (frontend)\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Client per operazioni admin (backend API)\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Database helper functions\nasync function getSupabaseClient() {\n    return supabaseAdmin;\n}\n// Table names\nconst TABLES = {\n    PRODUCTS: 'products',\n    CART: 'cart',\n    ORDERS: 'orders',\n    USERS: 'users'\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcart%2Froute&page=%2Fapi%2Fcart%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcart%2Froute.ts&appDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CAGtech%5CE-commerce%5Cagxexperience-store-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();