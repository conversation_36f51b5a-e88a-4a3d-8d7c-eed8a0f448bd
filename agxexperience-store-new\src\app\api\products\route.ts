import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin, TABLES } from '@/lib/supabase';
import { Product, ApiResponse } from '@/types';

// 🔄 Mock data for development when MongoDB is not available
const mockProducts: Product[] = [
  {
    _id: '1',
    name: 'Bot Telegram AI Premium',
    description: 'Bot Telegram personalizzato con intelligenza artificiale avanzata per automatizzare le tue conversazioni e gestire il customer service.',
    price: 299.99,
    slug: 'bot-telegram-ai-premium',
    tags: ['telegram', 'ai', 'automation', 'premium'],
    features: ['AI Conversazionale', 'Integrazione API', 'Dashboard Analytics', 'Support 24/7'],
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    _id: '2',
    name: 'Portfolio Website Futuristico',
    description: 'Sito web portfolio con design futuristico, animazioni 3D e integrazione AI per mostrare i tuoi progetti in modo innovativo.',
    price: 599.99,
    slug: 'portfolio-website-futuristico',
    tags: ['portfolio', 'website', 'design', '3d'],
    features: ['Design 3D', 'Animazioni Avanzate', 'SEO Ottimizzato', 'Mobile Responsive'],
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    _id: '3',
    name: 'Template Notion Pro',
    description: 'Template Notion professionale per organizzare progetti, task e knowledge base con sistema di automazione integrato.',
    price: 49.99,
    slug: 'template-notion-pro',
    tags: ['notion', 'productivity', 'template', 'organization'],
    features: ['Dashboard Completa', 'Automazioni', 'Template Multipli', 'Guida Setup'],
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    _id: '4',
    name: 'Sistema Automazione Business',
    description: 'Suite completa di automazioni per il tuo business: email marketing, social media, CRM e analytics integrati.',
    price: 899.99,
    slug: 'sistema-automazione-business',
    tags: ['automation', 'business', 'marketing', 'crm'],
    features: ['Email Marketing', 'Social Automation', 'CRM Integrato', 'Analytics Avanzati'],
    created_at: new Date(),
    updated_at: new Date()
  }
];

// GET - Fetch products with filters
export async function GET(request: NextRequest) {
  // Extract parameters outside try-catch for error handling access
  const { searchParams } = new URL(request.url);
  const category = searchParams.get('category');
  const search = searchParams.get('search');
  const minPrice = searchParams.get('minPrice');
  const maxPrice = searchParams.get('maxPrice');
  const tags = searchParams.get('tags');
  const sortBy = searchParams.get('sortBy') || 'created_at';
  const sortOrder = searchParams.get('sortOrder') || 'desc';

  try {

    // Build Supabase query
    let query = supabaseAdmin.from(TABLES.PRODUCTS).select('*');

    // Category filter
    if (category && category !== 'all') {
      query = query.contains('tags', [category]);
    }

    // Search filter
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    // Price filter
    if (minPrice) {
      query = query.gte('price', parseFloat(minPrice));
    }
    if (maxPrice) {
      query = query.lte('price', parseFloat(maxPrice));
    }

    // Tags filter
    if (tags) {
      const tagArray = tags.split(',').map(tag => tag.trim());
      query = query.overlaps('tags', tagArray);
    }

    // Sort configuration
    const ascending = sortOrder === 'asc';
    query = query.order(sortBy, { ascending });

    const { data: products, error } = await query;

    if (error) {
      throw error;
    }

    // Get all unique categories (tags) for filter options
    const { data: allProducts } = await supabaseAdmin.from(TABLES.PRODUCTS).select('tags');
    const allTags = [...new Set(allProducts?.flatMap((p: any) => p.tags) || [])];
    const categories = allTags.sort();

    // Get price range from Supabase
    const { data: priceData } = await supabaseAdmin
      .from(TABLES.PRODUCTS)
      .select('price')
      .order('price', { ascending: true });

    const prices = priceData?.map(p => p.price) || [];
    const priceRange = [{
      minPrice: prices.length > 0 ? Math.min(...prices) : 0,
      maxPrice: prices.length > 0 ? Math.max(...prices) : 1000
    }];

    // Transform Supabase products to include compatibility fields
    const transformedProducts = (products || []).map(product => ({
      ...product,
      _id: product.id, // Add MongoDB-style _id for compatibility
      slug: product.slug || product.id, // Use slug if available, otherwise use id
      tags: product.tags || [], // Ensure tags is always an array
      features: product.features || [], // Ensure features is always an array
      image_url: product.image_url || '', // Ensure image_url is always a string
      created_at: new Date(product.created_at),
      updated_at: new Date(product.updated_at || product.created_at)
    }));

    return NextResponse.json<ApiResponse<{
      products: Product[];
      categories: string[];
      priceRange: { min: number; max: number };
      totalCount: number;
      filters: any;
    }>>({
      success: true,
      data: {
        products: transformedProducts,
        categories,
        priceRange: priceRange[0] ? {
          min: priceRange[0].minPrice,
          max: priceRange[0].maxPrice
        } : { min: 0, max: 1000 },
        totalCount: transformedProducts.length,
        filters: { category, search, minPrice, maxPrice, tags, sortBy, sortOrder }
      },
      message: `Found ${transformedProducts.length} products`
    });

  } catch (error) {
    console.error('Products GET Error:', error);

    // Check if it's a MongoDB SSL error
    if (error instanceof Error && error.message.includes('SSL')) {
      console.log('🔧 MongoDB SSL Error detected - Using mock data for development...');
    } else {
      console.log('🔄 Database connection failed - Using mock data for development...');
    }

    // Apply filters to mock data
    let filteredProducts = [...mockProducts];

    // Category filter
    if (category && category !== 'all') {
      filteredProducts = filteredProducts.filter(p => p.tags.includes(category));
    }

    // Search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filteredProducts = filteredProducts.filter(p =>
        p.name.toLowerCase().includes(searchLower) ||
        p.description.toLowerCase().includes(searchLower) ||
        p.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // Return mock data when MongoDB is not available
    return NextResponse.json<ApiResponse<{ products: Product[], total: number, page: number, limit: number }>>({
      success: true,
      data: {
        products: filteredProducts,
        total: filteredProducts.length,
        page: 1,
        limit: filteredProducts.length
      },
      message: 'Using development mock data - MongoDB connection failed'
    });
  }
}

// POST - Create new product
export async function POST(request: NextRequest) {
  try {
    const productData = await request.json();

    // Validate required fields
    const { name, description, price, slug, tags } = productData;
    if (!name || !description || !price || !slug || !tags?.length) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Missing required fields: name, description, price, slug, tags'
      }, { status: 400 });
    }

    const db = await getDatabase();

    // Check if slug already exists
    const existingProduct = await db.collection(COLLECTIONS.PRODUCTS)
      .findOne({ slug });

    if (existingProduct) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Product with this slug already exists'
      }, { status: 409 });
    }

    // Create new product
    const newProduct: Omit<Product, '_id'> = {
      name,
      description,
      price: Number(price),
      slug,
      image_url: productData.image_url || '',
      tags: Array.isArray(tags) ? tags : [],
      features: Array.isArray(productData.features) ? productData.features : [],
      created_at: new Date(),
      updated_at: new Date()
    };

    const result = await db.collection(COLLECTIONS.PRODUCTS).insertOne(newProduct);

    return NextResponse.json<ApiResponse<Product>>({
      success: true,
      data: { ...newProduct, _id: result.insertedId.toString() },
      message: 'Product created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Products POST Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to create product'
    }, { status: 500 });
  }
}

// PUT - Update existing product
export async function PUT(request: NextRequest) {
  try {
    const productData = await request.json();
    const { _id, ...updateData } = productData;

    if (!_id) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Product ID is required'
      }, { status: 400 });
    }

    const db = await getDatabase();

    // Check if product exists
    const existingProduct = await db.collection(COLLECTIONS.PRODUCTS)
      .findOne({ _id });

    if (!existingProduct) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Product not found'
      }, { status: 404 });
    }

    // Update product
    const updatedProduct = {
      ...updateData,
      price: Number(updateData.price),
      updated_at: new Date()
    };

    await db.collection(COLLECTIONS.PRODUCTS).updateOne(
      { _id },
      { $set: updatedProduct }
    );

    const result = await db.collection(COLLECTIONS.PRODUCTS)
      .findOne({ _id });

    return NextResponse.json<ApiResponse<Product>>({
      success: true,
      data: result,
      message: 'Product updated successfully'
    });

  } catch (error) {
    console.error('Products PUT Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to update product'
    }, { status: 500 });
  }
}

// DELETE - Delete product
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('id');

    if (!productId) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Product ID is required'
      }, { status: 400 });
    }

    const db = await getDatabase();

    const result = await db.collection(COLLECTIONS.PRODUCTS)
      .deleteOne({ _id: productId });

    if (result.deletedCount === 0) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: 'Product not found'
      }, { status: 404 });
    }

    return NextResponse.json<ApiResponse>({
      success: true,
      message: 'Product deleted successfully'
    });

  } catch (error) {
    console.error('Products DELETE Error:', error);
    return NextResponse.json<ApiResponse>({
      success: false,
      error: 'Failed to delete product'
    }, { status: 500 });
  }
}
