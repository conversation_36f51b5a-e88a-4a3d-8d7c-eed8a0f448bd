-- AGXexperience Store - Sample Data (Run this after basic setup)
-- Execute this in Supabase SQL Editor AFTER running 01-basic-setup.sql

-- Insert sample user profiles
INSERT INTO user_profiles (session_id, email, first_name, last_name, interaction_count, interests, preferences, emotional_profile, created_at, last_seen) VALUES
('session_1753401234567_abc123', '<EMAIL>', '<PERSON>', '<PERSON>', 25, ARRAY['AI Tools', 'Automation'], '{"communication_style": "professional", "product_categories": ["AI Tools", "Automation"], "price_range": "premium"}', '{"current_mood": "curious", "engagement_level": 0.8, "conversation_depth": "deep"}', NOW() - INTERVAL '1 week', NOW() - INTERVAL '30 minutes'),
('session_1753401234568_def456', '<EMAIL>', 'Giulia', '<PERSON>ianchi', 18, ARRAY['E-commerce', 'Marketing'], '{"communication_style": "casual", "product_categories": ["E-commerce", "Marketing"], "price_range": "medium"}', '{"current_mood": "excited", "engagement_level": 0.9, "conversation_depth": "medium"}', NOW() - INTERVAL '2 weeks', NOW() - INTERVAL '1 hour'),
('session_1753401234569_ghi789', '<EMAIL>', 'Luca', 'Verdi', 42, ARRAY['AI Tools', 'Analytics'], '{"communication_style": "technical", "product_categories": ["AI Tools", "Analytics"], "price_range": "premium"}', '{"current_mood": "focused", "engagement_level": 0.7, "conversation_depth": "deep"}', NOW() - INTERVAL '1 month', NOW() - INTERVAL '2 hours'),
('session_1753401234570_jkl012', '<EMAIL>', 'Anna', 'Ferrari', 12, ARRAY['Social Media', 'Content'], '{"communication_style": "friendly", "product_categories": ["Social Media", "Content"], "price_range": "budget"}', '{"current_mood": "interested", "engagement_level": 0.6, "conversation_depth": "surface"}', NOW() - INTERVAL '5 days', NOW() - INTERVAL '1 day'),
('session_1753401234571_mno345', '<EMAIL>', 'Paolo', 'Russo', 35, ARRAY['Automation', 'Analytics'], '{"communication_style": "professional", "product_categories": ["Automation", "Analytics"], "price_range": "premium"}', '{"current_mood": "analytical", "engagement_level": 0.8, "conversation_depth": "deep"}', NOW() - INTERVAL '3 weeks', NOW() - INTERVAL '30 minutes')
ON CONFLICT (session_id) DO NOTHING;

-- Insert sample products (with category column)
INSERT INTO products (id, name, description, price, category, tags, is_active) VALUES
('ai-content-generator', 'AI Content Generator', 'Genera contenuti di alta qualità con intelligenza artificiale avanzata', 299.00, 'AI Tools', ARRAY['AI', 'Content', 'Writing'], true),
('bot-telegram-ai', 'Bot Telegram AI Premium', 'Bot Telegram intelligente per automazione e customer service', 599.00, 'Automation', ARRAY['Bot', 'Telegram', 'AI'], true),
('ecommerce-setup', 'E-commerce Store Setup', 'Setup completo negozio e-commerce con AI integrata', 199.00, 'E-commerce', ARRAY['E-commerce', 'Setup', 'Store'], true),
('social-media-ai', 'Social Media Manager AI', 'Gestione automatica social media con AI', 399.00, 'Marketing', ARRAY['Social Media', 'AI', 'Marketing'], true),
('ai-analytics', 'AI Analytics Dashboard', 'Dashboard analytics avanzata con insights AI', 799.00, 'Analytics', ARRAY['Analytics', 'AI', 'Dashboard'], true),
('seo-optimizer', 'SEO Optimizer AI', 'Ottimizzazione SEO automatica con intelligenza artificiale', 149.00, 'Marketing', ARRAY['SEO', 'AI', 'Optimization'], true),
('ai-image-generator', 'AI Image Generator', 'Generatore di immagini AI per contenuti creativi', 249.00, 'AI Tools', ARRAY['AI', 'Images', 'Creative'], true),
('complete-ai-suite', 'Complete AI Suite', 'Suite completa di strumenti AI per business', 899.00, 'AI Tools', ARRAY['AI', 'Suite', 'Business'], true)
ON CONFLICT (id) DO NOTHING;

-- Insert sample orders
INSERT INTO orders (customer_email, customer_name, total_amount, status, created_at) VALUES
('<EMAIL>', 'Marco Rossi', 299.00, 'completed', NOW() - INTERVAL '1 day'),
('<EMAIL>', 'Giulia Bianchi', 599.00, 'processing', NOW() - INTERVAL '2 days'),
('<EMAIL>', 'Luca Verdi', 199.00, 'completed', NOW() - INTERVAL '3 days'),
('<EMAIL>', 'Anna Ferrari', 399.00, 'pending', NOW() - INTERVAL '4 days'),
('<EMAIL>', 'Paolo Russo', 799.00, 'completed', NOW() - INTERVAL '5 days'),
('<EMAIL>', 'Sara Marino', 149.00, 'processing', NOW() - INTERVAL '6 days'),
('<EMAIL>', 'Davide Costa', 899.00, 'completed', NOW() - INTERVAL '7 days'),
('<EMAIL>', 'Elena Ricci', 249.00, 'cancelled', NOW() - INTERVAL '8 days'),
('<EMAIL>', 'Francesco Bruno', 599.00, 'completed', NOW() - INTERVAL '10 days'),
('<EMAIL>', 'Chiara Galli', 399.00, 'completed', NOW() - INTERVAL '12 days')
ON CONFLICT DO NOTHING;

-- Insert order items (this will work after orders are inserted)
DO $$
DECLARE
    order_record RECORD;
BEGIN
    -- Insert order items for each order
    FOR order_record IN SELECT id, customer_email, total_amount FROM orders LOOP
        CASE 
            WHEN order_record.customer_email = '<EMAIL>' THEN
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'ai-content-generator', 'AI Content Generator', 299.00, 1, 299.00)
                ON CONFLICT DO NOTHING;
            WHEN order_record.customer_email = '<EMAIL>' THEN
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'bot-telegram-ai', 'Bot Telegram AI Premium', 599.00, 1, 599.00)
                ON CONFLICT DO NOTHING;
            WHEN order_record.customer_email = '<EMAIL>' THEN
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'ecommerce-setup', 'E-commerce Store Setup', 199.00, 1, 199.00)
                ON CONFLICT DO NOTHING;
            WHEN order_record.customer_email = '<EMAIL>' THEN
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'social-media-ai', 'Social Media Manager AI', 399.00, 1, 399.00)
                ON CONFLICT DO NOTHING;
            WHEN order_record.customer_email = '<EMAIL>' THEN
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'ai-analytics', 'AI Analytics Dashboard', 799.00, 1, 799.00)
                ON CONFLICT DO NOTHING;
            WHEN order_record.customer_email = '<EMAIL>' THEN
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'seo-optimizer', 'SEO Optimizer AI', 149.00, 1, 149.00)
                ON CONFLICT DO NOTHING;
            WHEN order_record.customer_email = '<EMAIL>' THEN
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'complete-ai-suite', 'Complete AI Suite', 899.00, 1, 899.00)
                ON CONFLICT DO NOTHING;
            WHEN order_record.customer_email = '<EMAIL>' THEN
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'ai-image-generator', 'AI Image Generator', 249.00, 1, 249.00)
                ON CONFLICT DO NOTHING;
            ELSE
                -- Default case for other orders
                INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
                VALUES (order_record.id, 'ai-content-generator', 'AI Content Generator', 299.00, 1, order_record.total_amount)
                ON CONFLICT DO NOTHING;
        END CASE;
    END LOOP;
END $$;

-- Insert analytics events
INSERT INTO analytics_events (session_id, event_type, event_data, page_url, created_at) VALUES
('session_1753401234567_abc123', 'page_view', '{"page": "homepage"}', '/', NOW() - INTERVAL '1 hour'),
('session_1753401234567_abc123', 'product_view', '{"product_id": "ai-content-generator"}', '/products/ai-content-generator', NOW() - INTERVAL '50 minutes'),
('session_1753401234567_abc123', 'add_to_cart', '{"product_id": "ai-content-generator", "quantity": 1}', '/shop', NOW() - INTERVAL '45 minutes'),
('session_1753401234568_def456', 'page_view', '{"page": "shop"}', '/shop', NOW() - INTERVAL '2 hours'),
('session_1753401234568_def456', 'product_view', '{"product_id": "bot-telegram-ai"}', '/products/bot-telegram-ai', NOW() - INTERVAL '1 hour 30 minutes'),
('session_1753401234569_ghi789', 'page_view', '{"page": "homepage"}', '/', NOW() - INTERVAL '3 hours'),
('session_1753401234569_ghi789', 'chat_interaction', '{"message_count": 5}', '/', NOW() - INTERVAL '2 hours 45 minutes'),
('session_1753401234570_jkl012', 'page_view', '{"page": "shop"}', '/shop', NOW() - INTERVAL '1 day'),
('session_1753401234571_mno345', 'product_view', '{"product_id": "ai-analytics"}', '/products/ai-analytics', NOW() - INTERVAL '45 minutes')
ON CONFLICT DO NOTHING;

-- Insert system metrics
INSERT INTO system_metrics (metric_type, metric_value, metric_unit, created_at) VALUES
('cpu_usage', 25.5, 'percent', NOW() - INTERVAL '5 minutes'),
('memory_usage', 68.2, 'percent', NOW() - INTERVAL '5 minutes'),
('disk_usage', 45.8, 'percent', NOW() - INTERVAL '5 minutes'),
('response_time', 245, 'milliseconds', NOW() - INTERVAL '5 minutes'),
('active_connections', 12, 'count', NOW() - INTERVAL '5 minutes'),
('cpu_usage', 23.1, 'percent', NOW() - INTERVAL '10 minutes'),
('memory_usage', 66.8, 'percent', NOW() - INTERVAL '10 minutes'),
('response_time', 198, 'milliseconds', NOW() - INTERVAL '10 minutes')
ON CONFLICT DO NOTHING;

-- Insert sample messages
INSERT INTO messages (session_id, user_message, ai_response, emotion_state, timestamp) VALUES
('session_1753401234567_abc123', 'Ciao, mi puoi aiutare con i prodotti AI?', 'Ciao! Sono AURORA, la tua assistente AI. Sarò felice di aiutarti a scoprire i nostri prodotti AI. Cosa ti interessa di più?', 'curious', NOW() - INTERVAL '1 hour'),
('session_1753401234567_abc123', 'Vorrei qualcosa per generare contenuti', 'Perfetto! Il nostro AI Content Generator è ideale per te. Genera contenuti di alta qualità per blog, social media e marketing. Vuoi saperne di più?', 'excited', NOW() - INTERVAL '55 minutes'),
('session_1753401234568_def456', 'Hai bot per Telegram?', 'Sì! Abbiamo il Bot Telegram AI Premium, perfetto per automazione e customer service. È molto popolare tra i nostri clienti business.', 'helpful', NOW() - INTERVAL '2 hours'),
('session_1753401234569_ghi789', 'Cerco soluzioni analytics avanzate', 'Ottima scelta! La nostra AI Analytics Dashboard offre insights avanzati con intelligenza artificiale. Perfetta per analisi approfondite dei dati.', 'professional', NOW() - INTERVAL '3 hours')
ON CONFLICT DO NOTHING;
