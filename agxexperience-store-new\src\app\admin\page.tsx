'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { Product } from '@/types';

interface DashboardStats {
  totalProducts: number;
  totalOrders: number;
  totalUsers: number;
  revenue: number;
  todayRevenue: number;
  monthlyRevenue: number;
  conversionRate: number;
  avgOrderValue: number;
  topProducts: Array<{id: string, name: string, sales: number, revenue: number}>;
  recentOrders: Array<{id: string, customer: string, amount: number, status: string, date: string}>;
  userGrowth: Array<{month: string, users: number}>;
  salesChart: Array<{date: string, sales: number, revenue: number}>;
}

interface Order {
  id: string;
  customer_email: string;
  total_amount: number;
  status: 'pending' | 'processing' | 'completed' | 'cancelled';
  created_at: string;
  items: Array<{product_name: string, quantity: number, price: number}>;
}

interface User {
  id: string;
  session_id: string;
  email?: string;
  interaction_count: number;
  last_seen: string;
  created_at: string;
  preferences: any;
}

export default function AdminPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'dashboard' | 'products' | 'orders' | 'users' | 'analytics' | 'settings'>('dashboard');
  const [products, setProducts] = useState<Product[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalOrders: 0,
    totalUsers: 0,
    revenue: 0,
    todayRevenue: 0,
    monthlyRevenue: 0,
    conversionRate: 0,
    avgOrderValue: 0,
    topProducts: [],
    recentOrders: [],
    userGrowth: [],
    salesChart: []
  });
  const [realTimeData, setRealTimeData] = useState({
    activeUsers: 0,
    todaySales: 0,
    pendingOrders: 0,
    systemHealth: 'excellent'
  });

  useEffect(() => {
    fetchAllData();
    // Real-time updates ogni 30 secondi
    const interval = setInterval(fetchRealTimeData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchAllData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchProducts(),
        fetchOrders(),
        fetchUsers(),
        fetchStats(),
        fetchRealTimeData()
      ]);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/products');
      const data = await response.json();
      if (data.success) {
        setProducts(data.data.products);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    }
  };

  const fetchOrders = async () => {
    try {
      const response = await fetch('/api/admin/orders');
      const data = await response.json();
      if (data.success) {
        setOrders(data.data);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
      // Mock data per demo
      setOrders([
        {
          id: 'ORD-001',
          customer_email: '<EMAIL>',
          total_amount: 299,
          status: 'completed',
          created_at: new Date().toISOString(),
          items: [{product_name: 'AI Content Generator', quantity: 1, price: 299}]
        },
        {
          id: 'ORD-002',
          customer_email: '<EMAIL>',
          total_amount: 599,
          status: 'processing',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          items: [{product_name: 'Bot Telegram AI Premium', quantity: 1, price: 599}]
        }
      ]);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/admin/users');
      const data = await response.json();
      if (data.success) {
        setUsers(data.data);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      // Mock data per demo
      setUsers([
        {
          id: '1',
          session_id: 'session_123',
          email: '<EMAIL>',
          interaction_count: 15,
          last_seen: new Date().toISOString(),
          created_at: new Date(Date.now() - 604800000).toISOString(),
          preferences: {communication_style: 'professional'}
        }
      ]);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/stats');
      const data = await response.json();
      if (data.success) {
        setStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
      // Calcolo stats dai dati disponibili
      const totalRevenue = orders.reduce((sum, order) => sum + order.total_amount, 0);
      const todayOrders = orders.filter(order =>
        new Date(order.created_at).toDateString() === new Date().toDateString()
      );
      const todayRevenue = todayOrders.reduce((sum, order) => sum + order.total_amount, 0);

      setStats(prev => ({
        ...prev,
        totalProducts: products.length,
        totalOrders: orders.length,
        totalUsers: users.length,
        revenue: totalRevenue,
        todayRevenue,
        monthlyRevenue: totalRevenue * 0.8, // Stima
        conversionRate: users.length > 0 ? (orders.length / users.length) * 100 : 0,
        avgOrderValue: orders.length > 0 ? totalRevenue / orders.length : 0,
        topProducts: products.slice(0, 5).map(p => ({
          id: p.id || p._id,
          name: p.name,
          sales: Math.floor(Math.random() * 50) + 10,
          revenue: p.price * (Math.floor(Math.random() * 50) + 10)
        })),
        recentOrders: orders.slice(0, 5).map(order => ({
          id: order.id,
          customer: order.customer_email,
          amount: order.total_amount,
          status: order.status,
          date: order.created_at
        })),
        userGrowth: generateMockGrowthData(),
        salesChart: generateMockSalesData()
      }));
    }
  };

  const fetchRealTimeData = async () => {
    try {
      const response = await fetch('/api/admin/realtime');
      const data = await response.json();
      if (data.success) {
        setRealTimeData(data.data);
      }
    } catch (error) {
      // Mock real-time data
      setRealTimeData({
        activeUsers: Math.floor(Math.random() * 50) + 10,
        todaySales: Math.floor(Math.random() * 10) + 1,
        pendingOrders: orders.filter(o => o.status === 'pending').length,
        systemHealth: Math.random() > 0.1 ? 'excellent' : 'good'
      });
    }
  };

  const generateMockGrowthData = () => {
    const months = ['Gen', 'Feb', 'Mar', 'Apr', 'Mag', 'Giu'];
    return months.map(month => ({
      month,
      users: Math.floor(Math.random() * 100) + 50
    }));
  };

  const generateMockSalesData = () => {
    const days = Array.from({length: 7}, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return date.toISOString().split('T')[0];
    });
    return days.map(date => ({
      date,
      sales: Math.floor(Math.random() * 20) + 5,
      revenue: Math.floor(Math.random() * 2000) + 500
    }));
  };

  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: '📊', badge: null },
    { id: 'products', label: 'Prodotti', icon: '📦', badge: products.length },
    { id: 'orders', label: 'Ordini', icon: '🛒', badge: realTimeData.pendingOrders },
    { id: 'users', label: 'Utenti', icon: '👥', badge: realTimeData.activeUsers },
    { id: 'analytics', label: 'Analytics', icon: '📈', badge: null },
    { id: 'settings', label: 'Impostazioni', icon: '⚙️', badge: null }
  ];

  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Real-time Status Bar */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-xl p-4 border border-green-500/30"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse" />
              <span className="text-white font-medium">Sistema Online</span>
            </div>
            <div className="text-sm text-gray-300">
              {realTimeData.activeUsers} utenti attivi • {realTimeData.todaySales} vendite oggi
            </div>
          </div>
          <div className="text-xs text-gray-400">
            Aggiornato: {new Date().toLocaleTimeString()}
          </div>
        </div>
      </motion.div>

      {/* KPI Cards Avanzate */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          {
            label: 'Revenue Totale',
            value: `€${stats.revenue.toLocaleString()}`,
            change: '+12.5%',
            changeType: 'positive',
            icon: '💰',
            color: 'from-yellow-500 to-orange-500',
            subtitle: `€${stats.todayRevenue} oggi`
          },
          {
            label: 'Ordini',
            value: stats.totalOrders,
            change: '+8.2%',
            changeType: 'positive',
            icon: '🛒',
            color: 'from-green-500 to-emerald-500',
            subtitle: `${realTimeData.pendingOrders} in attesa`
          },
          {
            label: 'Conversioni',
            value: `${stats.conversionRate.toFixed(1)}%`,
            change: '+2.1%',
            changeType: 'positive',
            icon: '📈',
            color: 'from-blue-500 to-cyan-500',
            subtitle: `€${stats.avgOrderValue.toFixed(0)} valore medio`
          },
          {
            label: 'Utenti Attivi',
            value: stats.totalUsers,
            change: '+15.3%',
            changeType: 'positive',
            icon: '👥',
            color: 'from-purple-500 to-pink-500',
            subtitle: `${realTimeData.activeUsers} online ora`
          }
        ].map((stat, index) => (
          <motion.div
            key={stat.label}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`bg-gradient-to-br ${stat.color} rounded-xl p-6 text-white relative overflow-hidden`}
          >
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute -right-4 -top-4 w-24 h-24 rounded-full bg-white/20" />
              <div className="absolute -left-2 -bottom-2 w-16 h-16 rounded-full bg-white/10" />
            </div>

            <div className="relative">
              <div className="flex items-center justify-between mb-4">
                <span className="text-3xl">{stat.icon}</span>
                <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                  stat.changeType === 'positive' ? 'bg-white/20 text-white' : 'bg-red-500/20 text-red-200'
                }`}>
                  {stat.change}
                </div>
              </div>

              <div>
                <p className="text-sm opacity-90 mb-1">{stat.label}</p>
                <p className="text-3xl font-bold mb-1">{stat.value}</p>
                <p className="text-xs opacity-75">{stat.subtitle}</p>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Charts e Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Chart */}
        <div className="bg-white/5 rounded-xl p-6 border border-white/10">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-white">Vendite Ultimi 7 Giorni</h3>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full" />
              <span className="text-xs text-gray-400">Revenue</span>
            </div>
          </div>

          <div className="h-48 flex items-end justify-between space-x-2">
            {stats.salesChart.map((day, index) => (
              <div key={day.date} className="flex-1 flex flex-col items-center">
                <motion.div
                  initial={{ height: 0 }}
                  animate={{ height: `${(day.revenue / 2000) * 100}%` }}
                  transition={{ delay: index * 0.1 }}
                  className="w-full bg-gradient-to-t from-blue-500 to-cyan-400 rounded-t-sm min-h-[4px] mb-2"
                />
                <span className="text-xs text-gray-400 transform -rotate-45">
                  {new Date(day.date).getDate()}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white/5 rounded-xl p-6 border border-white/10">
          <h3 className="text-lg font-semibold text-white mb-6">Top Prodotti</h3>
          <div className="space-y-4">
            {stats.topProducts.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-3 bg-white/5 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                    {index + 1}
                  </div>
                  <div>
                    <p className="text-white font-medium text-sm">{product.name}</p>
                    <p className="text-gray-400 text-xs">{product.sales} vendite</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-white font-bold">€{product.revenue}</p>
                  <div className="w-16 h-1 bg-gray-700 rounded-full overflow-hidden">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${(product.sales / 50) * 100}%` }}
                      transition={{ delay: index * 0.1 + 0.5 }}
                      className="h-full bg-gradient-to-r from-purple-500 to-blue-500"
                    />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Orders e System Health */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Orders */}
        <div className="lg:col-span-2 bg-white/5 rounded-xl p-6 border border-white/10">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-white">Ordini Recenti</h3>
            <motion.button
              whileHover={{ scale: 1.05 }}
              className="text-xs text-blue-400 hover:text-blue-300"
            >
              Vedi tutti
            </motion.button>
          </div>

          <div className="space-y-3">
            {stats.recentOrders.map((order, index) => (
              <motion.div
                key={order.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    order.status === 'completed' ? 'bg-green-400' :
                    order.status === 'processing' ? 'bg-yellow-400' :
                    order.status === 'pending' ? 'bg-blue-400' : 'bg-red-400'
                  }`} />
                  <div>
                    <p className="text-white font-medium text-sm">{order.id}</p>
                    <p className="text-gray-400 text-xs">{order.customer}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-white font-bold">€{order.amount}</p>
                  <p className="text-gray-400 text-xs">
                    {new Date(order.date).toLocaleDateString()}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* System Health */}
        <div className="bg-white/5 rounded-xl p-6 border border-white/10">
          <h3 className="text-lg font-semibold text-white mb-6">System Health</h3>

          <div className="space-y-4">
            {[
              { label: 'Database', status: 'excellent', value: '99.9%' },
              { label: 'API Response', status: 'good', value: '245ms' },
              { label: 'Storage', status: 'excellent', value: '78%' },
              { label: 'Memory', status: 'good', value: '64%' }
            ].map((metric, index) => (
              <div key={metric.label} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-300">{metric.label}</span>
                  <span className="text-xs text-white font-medium">{metric.value}</span>
                </div>
                <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: metric.status === 'excellent' ? '95%' : '75%' }}
                    transition={{ delay: index * 0.2 }}
                    className={`h-full ${
                      metric.status === 'excellent' ? 'bg-green-500' : 'bg-yellow-500'
                    }`}
                  />
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 p-3 bg-green-500/20 rounded-lg border border-green-500/30">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span className="text-green-300 text-sm font-medium">Tutti i sistemi operativi</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderProducts = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">Gestione Prodotti</h3>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg font-medium"
        >
          Aggiungi Prodotto
        </motion.button>
      </div>

      <div className="bg-white/5 rounded-xl border border-white/10 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-white/5">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Nome</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Prezzo</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Categoria</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Azioni</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/5">
              {products.map((product) => (
                <tr key={product.id || product._id} className="hover:bg-white/5">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-white">{product.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-white">€{product.price}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{product.tags[0] || 'N/A'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                    <button className="text-blue-400 hover:text-blue-300">Modifica</button>
                    <button className="text-red-400 hover:text-red-300">Elimina</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderAnalytics = () => (
    <div className="space-y-6">
      {/* Analytics Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Analytics Avanzate</h2>
        <div className="flex items-center space-x-3">
          <select className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white text-sm">
            <option>Ultimi 7 giorni</option>
            <option>Ultimi 30 giorni</option>
            <option>Ultimi 3 mesi</option>
          </select>
          <motion.button
            whileHover={{ scale: 1.05 }}
            className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg text-sm font-medium"
          >
            Esporta Report
          </motion.button>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {[
          { label: 'Tasso di Conversione', value: '3.2%', change: '+0.5%', icon: '🎯' },
          { label: 'Valore Medio Ordine', value: '€425', change: '+€23', icon: '💳' },
          { label: 'Retention Rate', value: '68%', change: '+12%', icon: '🔄' }
        ].map((metric, index) => (
          <motion.div
            key={metric.label}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white/5 rounded-xl p-6 border border-white/10"
          >
            <div className="flex items-center justify-between mb-4">
              <span className="text-2xl">{metric.icon}</span>
              <span className="text-xs text-green-400 bg-green-400/20 px-2 py-1 rounded-full">
                {metric.change}
              </span>
            </div>
            <h3 className="text-2xl font-bold text-white mb-1">{metric.value}</h3>
            <p className="text-sm text-gray-400">{metric.label}</p>
          </motion.div>
        ))}
      </div>

      {/* Advanced Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Growth Chart */}
        <div className="bg-white/5 rounded-xl p-6 border border-white/10">
          <h3 className="text-lg font-semibold text-white mb-6">Crescita Utenti</h3>
          <div className="h-64 flex items-end justify-between space-x-3">
            {stats.userGrowth.map((month, index) => (
              <div key={month.month} className="flex-1 flex flex-col items-center">
                <motion.div
                  initial={{ height: 0 }}
                  animate={{ height: `${(month.users / 150) * 100}%` }}
                  transition={{ delay: index * 0.2 }}
                  className="w-full bg-gradient-to-t from-purple-500 to-pink-400 rounded-t-lg min-h-[8px] mb-3"
                />
                <span className="text-xs text-gray-400 mb-1">{month.users}</span>
                <span className="text-xs text-gray-500">{month.month}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Revenue Breakdown */}
        <div className="bg-white/5 rounded-xl p-6 border border-white/10">
          <h3 className="text-lg font-semibold text-white mb-6">Breakdown Revenue</h3>
          <div className="space-y-4">
            {[
              { category: 'AI Tools', amount: 12500, percentage: 45, color: 'bg-blue-500' },
              { category: 'Bot Telegram', amount: 8900, percentage: 32, color: 'bg-purple-500' },
              { category: 'E-commerce Setup', amount: 4200, percentage: 15, color: 'bg-green-500' },
              { category: 'Altri', amount: 2200, percentage: 8, color: 'bg-yellow-500' }
            ].map((item, index) => (
              <motion.div
                key={item.category}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="space-y-2"
              >
                <div className="flex items-center justify-between">
                  <span className="text-white text-sm">{item.category}</span>
                  <span className="text-white font-bold">€{item.amount.toLocaleString()}</span>
                </div>
                <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${item.percentage}%` }}
                    transition={{ delay: index * 0.1 + 0.5 }}
                    className={`h-full ${item.color}`}
                  />
                </div>
                <div className="flex items-center justify-between text-xs text-gray-400">
                  <span>{item.percentage}% del totale</span>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Detailed Tables */}
      <div className="bg-white/5 rounded-xl p-6 border border-white/10">
        <h3 className="text-lg font-semibold text-white mb-6">Performance Dettagliate</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-white/10">
                <th className="text-left text-xs font-medium text-gray-300 uppercase tracking-wider py-3">Metrica</th>
                <th className="text-left text-xs font-medium text-gray-300 uppercase tracking-wider py-3">Oggi</th>
                <th className="text-left text-xs font-medium text-gray-300 uppercase tracking-wider py-3">Ieri</th>
                <th className="text-left text-xs font-medium text-gray-300 uppercase tracking-wider py-3">Variazione</th>
                <th className="text-left text-xs font-medium text-gray-300 uppercase tracking-wider py-3">Trend</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/5">
              {[
                { metric: 'Visite', today: '1,234', yesterday: '1,156', change: '+6.7%', trend: '📈' },
                { metric: 'Conversioni', today: '42', yesterday: '38', change: '+10.5%', trend: '📈' },
                { metric: 'Revenue', today: '€2,340', yesterday: '€2,180', change: '+7.3%', trend: '📈' },
                { metric: 'Bounce Rate', today: '32%', yesterday: '35%', change: '-8.6%', trend: '📉' }
              ].map((row, index) => (
                <motion.tr
                  key={row.metric}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: index * 0.1 }}
                  className="hover:bg-white/5"
                >
                  <td className="py-4 text-white font-medium">{row.metric}</td>
                  <td className="py-4 text-white">{row.today}</td>
                  <td className="py-4 text-gray-400">{row.yesterday}</td>
                  <td className={`py-4 font-medium ${row.change.startsWith('+') ? 'text-green-400' : 'text-red-400'}`}>
                    {row.change}
                  </td>
                  <td className="py-4 text-lg">{row.trend}</td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderOrders = () => (
    <div className="space-y-6">
      {/* Orders Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Gestione Ordini</h2>
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2 bg-white/10 rounded-lg px-3 py-2">
            <span className="text-sm text-gray-300">Filtro:</span>
            <select className="bg-transparent text-white text-sm border-none outline-none">
              <option>Tutti</option>
              <option>Completati</option>
              <option>In elaborazione</option>
              <option>In attesa</option>
            </select>
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            className="px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg text-sm font-medium"
          >
            Esporta Ordini
          </motion.button>
        </div>
      </div>

      {/* Orders Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {[
          { label: 'Totale Ordini', value: orders.length, color: 'from-blue-500 to-blue-600' },
          { label: 'Completati', value: orders.filter(o => o.status === 'completed').length, color: 'from-green-500 to-green-600' },
          { label: 'In Elaborazione', value: orders.filter(o => o.status === 'processing').length, color: 'from-yellow-500 to-yellow-600' },
          { label: 'In Attesa', value: orders.filter(o => o.status === 'pending').length, color: 'from-red-500 to-red-600' }
        ].map((stat, index) => (
          <motion.div
            key={stat.label}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`bg-gradient-to-r ${stat.color} rounded-lg p-4 text-white`}
          >
            <p className="text-2xl font-bold">{stat.value}</p>
            <p className="text-sm opacity-90">{stat.label}</p>
          </motion.div>
        ))}
      </div>

      {/* Orders Table */}
      <div className="bg-white/5 rounded-xl border border-white/10 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-white/5">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">ID Ordine</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Cliente</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Prodotti</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Totale</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Data</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Azioni</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/5">
              {orders.map((order, index) => (
                <motion.tr
                  key={order.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: index * 0.1 }}
                  className="hover:bg-white/5"
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">{order.id}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{order.customer_email}</td>
                  <td className="px-6 py-4 text-sm text-gray-300">
                    {order.items.map(item => item.product_name).join(', ')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-white">€{order.total_amount}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      order.status === 'completed' ? 'bg-green-500/20 text-green-300' :
                      order.status === 'processing' ? 'bg-yellow-500/20 text-yellow-300' :
                      order.status === 'pending' ? 'bg-blue-500/20 text-blue-300' :
                      'bg-red-500/20 text-red-300'
                    }`}>
                      {order.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                    {new Date(order.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                    <button className="text-blue-400 hover:text-blue-300">Visualizza</button>
                    <button className="text-green-400 hover:text-green-300">Modifica</button>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderUsers = () => (
    <div className="space-y-6">
      {/* Users Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Gestione Utenti</h2>
        <motion.button
          whileHover={{ scale: 1.05 }}
          className="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg text-sm font-medium"
        >
          Esporta Utenti
        </motion.button>
      </div>

      {/* Users Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {[
          { label: 'Utenti Totali', value: users.length, icon: '👥', color: 'from-blue-500 to-blue-600' },
          { label: 'Attivi Oggi', value: realTimeData.activeUsers, icon: '🟢', color: 'from-green-500 to-green-600' },
          { label: 'Nuovi Questa Settimana', value: Math.floor(users.length * 0.1), icon: '✨', color: 'from-purple-500 to-purple-600' }
        ].map((stat, index) => (
          <motion.div
            key={stat.label}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`bg-gradient-to-r ${stat.color} rounded-xl p-6 text-white`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm opacity-90">{stat.label}</p>
                <p className="text-3xl font-bold">{stat.value}</p>
              </div>
              <span className="text-3xl opacity-80">{stat.icon}</span>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Users Table */}
      <div className="bg-white/5 rounded-xl border border-white/10 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-white/5">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Utente</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Email</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Interazioni</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Ultimo Accesso</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Registrato</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Azioni</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/5">
              {users.map((user, index) => (
                <motion.tr
                  key={user.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: index * 0.1 }}
                  className="hover:bg-white/5"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                        {user.email?.charAt(0).toUpperCase() || 'U'}
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-white">{user.session_id.slice(0, 8)}...</p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{user.email || 'N/A'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-white font-bold">{user.interaction_count}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                    {new Date(user.last_seen).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                    {new Date(user.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                    <button className="text-blue-400 hover:text-blue-300">Visualizza</button>
                    <button className="text-yellow-400 hover:text-yellow-300">Messaggi</button>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard': return renderDashboard();
      case 'products': return renderProducts();
      case 'orders': return renderOrders();
      case 'users': return renderUsers();
      case 'analytics': return renderAnalytics();
      case 'settings': return <div className="text-white">Impostazioni - In sviluppo</div>;
      default: return renderDashboard();
    }
  };

  return (
    <div className="min-h-screen bg-black">
      {/* Background Effects */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/10 via-black to-blue-900/10" />
      </div>

      {/* Header */}
      <motion.header
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="relative z-50 bg-black/90 backdrop-blur-xl border-b border-white/10"
      >
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <motion.button
                whileHover={{ scale: 1.02 }}
                onClick={() => router.push('/')}
                className="flex items-center space-x-3 text-white hover:text-purple-300 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                <span className="text-sm font-medium">Torna al Sito</span>
              </motion.button>
              <div className="h-6 w-px bg-white/20" />
              <h1 className="text-xl font-bold text-white">Admin Dashboard</h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-400">AGXexperience Store</span>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            </div>
          </div>
        </div>
      </motion.header>

      {/* Main Content */}
      <div className="relative z-40 max-w-7xl mx-auto px-6 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <motion.aside
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="lg:w-64 space-y-2"
          >
            {tabs.map((tab) => (
              <motion.button
                key={tab.id}
                whileHover={{ scale: 1.02, x: 5 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setActiveTab(tab.id as any)}
                className={`w-full flex items-center justify-between px-4 py-3 rounded-lg font-medium transition-all ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg'
                    : 'text-gray-300 hover:bg-white/5 hover:text-white'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <span className="text-lg">{tab.icon}</span>
                  <span>{tab.label}</span>
                </div>

                {/* Badge */}
                {tab.badge !== null && tab.badge > 0 && (
                  <motion.span
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className={`px-2 py-1 text-xs font-bold rounded-full ${
                      activeTab === tab.id
                        ? 'bg-white/20 text-white'
                        : 'bg-purple-500/20 text-purple-300'
                    }`}
                  >
                    {tab.badge}
                  </motion.span>
                )}
              </motion.button>
            ))}
          </motion.aside>

          {/* Main Content */}
          <motion.main
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="flex-1"
          >
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin" />
              </div>
            ) : (
              renderContent()
            )}
          </motion.main>
        </div>
      </div>
    </div>
  );
}
