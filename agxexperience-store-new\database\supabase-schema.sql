-- AGXexperience Store - Complete Supabase Schema
-- Run this in Supabase SQL Editor

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_email VARCHAR(255) NOT NULL,
    customer_name VARCHAR(255),
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'cancelled', 'refunded')),
    payment_intent_id VARCHAR(255),
    stripe_session_id VARCHAR(255),
    shipping_address JSONB,
    billing_address JSONB,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order items table
CREATE TABLE IF NOT EXISTS order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    product_id VARCHAR(255) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_price DECIMAL(10,2) NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Analytics events table
CREATE TABLE IF NOT EXISTS analytics_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB,
    page_url VARCHAR(500),
    user_agent TEXT,
    ip_address INET,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System metrics table
CREATE TABLE IF NOT EXISTS system_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_type VARCHAR(100) NOT NULL,
    metric_value DECIMAL(10,4) NOT NULL,
    metric_unit VARCHAR(50),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Admin logs table
CREATE TABLE IF NOT EXISTS admin_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    admin_session VARCHAR(255) NOT NULL,
    action VARCHAR(255) NOT NULL,
    target_type VARCHAR(100),
    target_id VARCHAR(255),
    old_data JSONB,
    new_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User profiles table (create if not exists)
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255),
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    phone VARCHAR(50),
    country VARCHAR(100),
    city VARCHAR(100),
    interests TEXT[] DEFAULT '{}',
    past_requests JSONB DEFAULT '[]',
    preferences JSONB DEFAULT '{}',
    interaction_count INTEGER DEFAULT 0,
    total_orders INTEGER DEFAULT 0,
    total_spent DECIMAL(10,2) DEFAULT 0,
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_order_date TIMESTAMP WITH TIME ZONE,
    marketing_consent BOOLEAN DEFAULT false,
    emotional_profile JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Messages table (for chat history)
CREATE TABLE IF NOT EXISTS messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) NOT NULL,
    user_message TEXT NOT NULL,
    ai_response TEXT NOT NULL,
    emotion_state VARCHAR(100),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table (if not exists from previous setup)
CREATE TABLE IF NOT EXISTS products (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    category VARCHAR(100),
    tags TEXT[] DEFAULT '{}',
    image_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Cart table
CREATE TABLE IF NOT EXISTS cart (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) NOT NULL,
    product_id VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_customer_email ON orders(customer_email);
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_session_id ON analytics_events(session_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_event_type ON analytics_events(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_events_created_at ON analytics_events(created_at);
CREATE INDEX IF NOT EXISTS idx_user_profiles_session_id ON user_profiles(session_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_last_seen ON user_profiles(last_seen);
CREATE INDEX IF NOT EXISTS idx_user_profiles_created_at ON user_profiles(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_session_id ON messages(session_id);
CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages(timestamp);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_is_active ON products(is_active);
CREATE INDEX IF NOT EXISTS idx_cart_session_id ON cart(session_id);
CREATE INDEX IF NOT EXISTS idx_cart_product_id ON cart(product_id);
CREATE INDEX IF NOT EXISTS idx_system_metrics_type ON system_metrics(metric_type);
CREATE INDEX IF NOT EXISTS idx_system_metrics_created_at ON system_metrics(created_at);

-- Create triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cart_updated_at BEFORE UPDATE ON cart
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for testing
INSERT INTO orders (customer_email, customer_name, total_amount, status, created_at) VALUES
('<EMAIL>', 'Marco Rossi', 299.00, 'completed', NOW() - INTERVAL '1 day'),
('<EMAIL>', 'Giulia Bianchi', 599.00, 'processing', NOW() - INTERVAL '2 days'),
('<EMAIL>', 'Luca Verdi', 199.00, 'completed', NOW() - INTERVAL '3 days'),
('<EMAIL>', 'Anna Ferrari', 399.00, 'pending', NOW() - INTERVAL '4 days'),
('<EMAIL>', 'Paolo Russo', 799.00, 'completed', NOW() - INTERVAL '5 days'),
('<EMAIL>', 'Sara Marino', 149.00, 'processing', NOW() - INTERVAL '6 days'),
('<EMAIL>', 'Davide Costa', 899.00, 'completed', NOW() - INTERVAL '7 days'),
('<EMAIL>', 'Elena Ricci', 249.00, 'cancelled', NOW() - INTERVAL '8 days')
ON CONFLICT DO NOTHING;

-- Insert sample user profiles
INSERT INTO user_profiles (session_id, email, first_name, last_name, interaction_count, interests, preferences, emotional_profile, created_at, last_seen) VALUES
('session_1753401234567_abc123', '<EMAIL>', 'Marco', 'Rossi', 25, ARRAY['AI Tools', 'Automation'], '{"communication_style": "professional", "product_categories": ["AI Tools", "Automation"], "price_range": "premium"}', '{"current_mood": "curious", "engagement_level": 0.8, "conversation_depth": "deep"}', NOW() - INTERVAL '1 week', NOW() - INTERVAL '30 minutes'),
('session_1753401234568_def456', '<EMAIL>', 'Giulia', 'Bianchi', 18, ARRAY['E-commerce', 'Marketing'], '{"communication_style": "casual", "product_categories": ["E-commerce", "Marketing"], "price_range": "medium"}', '{"current_mood": "excited", "engagement_level": 0.9, "conversation_depth": "medium"}', NOW() - INTERVAL '2 weeks', NOW() - INTERVAL '1 hour'),
('session_1753401234569_ghi789', '<EMAIL>', 'Luca', 'Verdi', 42, ARRAY['AI Tools', 'Analytics'], '{"communication_style": "technical", "product_categories": ["AI Tools", "Analytics"], "price_range": "premium"}', '{"current_mood": "focused", "engagement_level": 0.7, "conversation_depth": "deep"}', NOW() - INTERVAL '1 month', NOW() - INTERVAL '2 hours'),
('session_1753401234570_jkl012', '<EMAIL>', 'Anna', 'Ferrari', 12, ARRAY['Social Media', 'Content'], '{"communication_style": "friendly", "product_categories": ["Social Media", "Content"], "price_range": "budget"}', '{"current_mood": "interested", "engagement_level": 0.6, "conversation_depth": "surface"}', NOW() - INTERVAL '5 days', NOW() - INTERVAL '1 day'),
('session_1753401234571_mno345', '<EMAIL>', 'Paolo', 'Russo', 35, ARRAY['Automation', 'Analytics'], '{"communication_style": "professional", "product_categories": ["Automation", "Analytics"], "price_range": "premium"}', '{"current_mood": "analytical", "engagement_level": 0.8, "conversation_depth": "deep"}', NOW() - INTERVAL '3 weeks', NOW() - INTERVAL '30 minutes')
ON CONFLICT (session_id) DO NOTHING;

-- Insert sample products
INSERT INTO products (id, name, description, price, category, tags, is_active) VALUES
('ai-content-generator', 'AI Content Generator', 'Genera contenuti di alta qualità con intelligenza artificiale avanzata', 299.00, 'AI Tools', ARRAY['AI', 'Content', 'Writing'], true),
('bot-telegram-ai', 'Bot Telegram AI Premium', 'Bot Telegram intelligente per automazione e customer service', 599.00, 'Automation', ARRAY['Bot', 'Telegram', 'AI'], true),
('ecommerce-setup', 'E-commerce Store Setup', 'Setup completo negozio e-commerce con AI integrata', 199.00, 'E-commerce', ARRAY['E-commerce', 'Setup', 'Store'], true),
('social-media-ai', 'Social Media Manager AI', 'Gestione automatica social media con AI', 399.00, 'Marketing', ARRAY['Social Media', 'AI', 'Marketing'], true),
('ai-analytics', 'AI Analytics Dashboard', 'Dashboard analytics avanzata con insights AI', 799.00, 'Analytics', ARRAY['Analytics', 'AI', 'Dashboard'], true)
ON CONFLICT (id) DO NOTHING;

-- Insert order items
INSERT INTO order_items (order_id, product_id, product_name, product_price, quantity, total_price)
SELECT
    o.id,
    'ai-content-generator',
    'AI Content Generator',
    299.00,
    1,
    299.00
FROM orders o WHERE o.customer_email = '<EMAIL>'
ON CONFLICT DO NOTHING;

-- Insert analytics events
INSERT INTO analytics_events (session_id, event_type, event_data, page_url, created_at) VALUES
('session_' || extract(epoch from now())::text, 'page_view', '{"page": "homepage"}', '/', NOW()),
('session_' || extract(epoch from now())::text, 'product_view', '{"product_id": "ai-content-generator"}', '/products/ai-content-generator', NOW() - INTERVAL '1 hour'),
('session_' || extract(epoch from now())::text, 'add_to_cart', '{"product_id": "ai-content-generator", "quantity": 1}', '/shop', NOW() - INTERVAL '2 hours')
ON CONFLICT DO NOTHING;

-- Insert system metrics
INSERT INTO system_metrics (metric_type, metric_value, metric_unit, created_at) VALUES
('cpu_usage', 25.5, 'percent', NOW()),
('memory_usage', 68.2, 'percent', NOW()),
('disk_usage', 45.8, 'percent', NOW()),
('response_time', 245, 'milliseconds', NOW()),
('active_connections', 12, 'count', NOW())
ON CONFLICT DO NOTHING;

-- Enable Row Level Security (RLS)
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE cart ENABLE ROW LEVEL SECURITY;

-- Create policies (adjust based on your auth requirements)
CREATE POLICY "Enable read access for all users" ON orders FOR SELECT USING (true);
CREATE POLICY "Enable read access for all users" ON order_items FOR SELECT USING (true);
CREATE POLICY "Enable read access for all users" ON analytics_events FOR SELECT USING (true);
CREATE POLICY "Enable read access for all users" ON system_metrics FOR SELECT USING (true);
CREATE POLICY "Enable read access for all users" ON admin_logs FOR SELECT USING (true);
CREATE POLICY "Enable read access for all users" ON user_profiles FOR SELECT USING (true);
CREATE POLICY "Enable read access for all users" ON messages FOR SELECT USING (true);
CREATE POLICY "Enable read access for all users" ON products FOR SELECT USING (true);
CREATE POLICY "Enable read access for all users" ON cart FOR SELECT USING (true);

-- Enable insert/update policies for application functionality
CREATE POLICY "Enable insert for all users" ON orders FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable insert for all users" ON order_items FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable insert for all users" ON analytics_events FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable insert for all users" ON system_metrics FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable insert for all users" ON user_profiles FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable insert for all users" ON messages FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable insert for all users" ON cart FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable update for all users" ON orders FOR UPDATE USING (true);
CREATE POLICY "Enable update for all users" ON user_profiles FOR UPDATE USING (true);
CREATE POLICY "Enable update for all users" ON products FOR UPDATE USING (true);
CREATE POLICY "Enable update for all users" ON cart FOR UPDATE USING (true);

-- Grant permissions
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
