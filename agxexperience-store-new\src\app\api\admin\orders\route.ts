import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    // Fetch real orders from Supabase with order items
    const { data: orders, error } = await supabaseAdmin
      .from('orders')
      .select(`
        *,
        order_items (
          id,
          product_id,
          product_name,
          product_price,
          quantity,
          total_price
        )
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching orders:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch orders from database' },
        { status: 500 }
      );
    }

    // Use the fetched orders data
    const ordersData = orders || [];

    // Calculate real statistics from orders data
    const stats = {
      total: ordersData.length,
      completed: ordersData.filter(o => o.status === 'completed').length,
      processing: ordersData.filter(o => o.status === 'processing').length,
      pending: ordersData.filter(o => o.status === 'pending').length,
      cancelled: ordersData.filter(o => o.status === 'cancelled').length,
      totalRevenue: ordersData.reduce((sum, order) => sum + parseFloat(order.total_amount || 0), 0)
    };

    return NextResponse.json({
      success: true,
      data: ordersData,
      total: ordersData.length,
      stats
    });

  } catch (error) {
    console.error('Error fetching orders:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch orders' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { orderId, status } = await request.json();

    // Update order status in database
    const { data, error } = await supabaseAdmin
      .from('orders')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', orderId)
      .select();

    if (error) throw error;

    return NextResponse.json({
      success: true,
      data: data[0],
      message: 'Order status updated successfully'
    });

  } catch (error) {
    console.error('Error updating order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update order' },
      { status: 500 }
    );
  }
}
